import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/notification_model.dart';
import '../models/trip_model.dart';
import '../models/booking_model.dart';
import 'notification_service.dart';

class TripReminderService {
  static final SupabaseClient _client = Supabase.instance.client;

  // Send trip reminders for upcoming trips
  static Future<void> sendTripReminders() async {
    try {
      final now = DateTime.now();

      // Send 24-hour reminders
      await _send24HourReminders(now);

      // Send 30-minute reminders
      await _send30MinuteReminders(now);

      if (kDebugMode) {
        print('✅ Trip reminders sent successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending trip reminders: $e');
      }
    }
  }

  // Send 24-hour reminders
  static Future<void> _send24HourReminders(DateTime now) async {
    final tomorrow = now.add(const Duration(days: 1));
    final tomorrowStart = DateTime(tomorrow.year, tomorrow.month, tomorrow.day);
    final tomorrowEnd = tomorrowStart.add(const Duration(days: 1));

    try {
      // Get trips departing tomorrow with accepted bookings
      final response = await _client
          .from('trips')
          .select(
              'id, from_city, to_city, departure_date, departure_time, bookings!inner(id, passenger_id, status, passenger:users!passenger_id(id, full_name))')
          .eq('bookings.status', 'accepted')
          .gte('departure_date', tomorrowStart.toIso8601String().split('T')[0])
          .lt('departure_date', tomorrowEnd.toIso8601String().split('T')[0]);

      for (final tripData in response) {
        final trip = TripModel.fromJson(tripData);
        final bookings = tripData['bookings'] as List;

        for (final bookingData in bookings) {
          final passengerId = bookingData['passenger_id'] as String;
          final passengerName =
              bookingData['passenger']?['full_name'] as String? ?? 'مسافر';

          final notification = NotificationModel.createTripReminder(
            userId: passengerId,
            tripId: trip.id,
            tripDestination: trip.toCity,
            departureTime: DateTime.parse(
                '${trip.departureDate.toIso8601String().split('T')[0]} ${trip.departureTime}'),
            reminderType: '24h',
          );

          await NotificationService.createNotification(notification);
        }
      }

      if (kDebugMode) {
        print('✅ 24-hour reminders sent for ${response.length} trips');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending 24-hour reminders: $e');
      }
    }
  }

  // Send 30-minute reminders
  static Future<void> _send30MinuteReminders(DateTime now) async {
    final in30Minutes = now.add(const Duration(minutes: 30));
    final in40Minutes = now.add(const Duration(minutes: 40));

    final today = DateTime(now.year, now.month, now.day);
    final todayStr = today.toIso8601String().split('T')[0];

    try {
      // Get trips departing in 30-40 minutes with accepted bookings
      final response = await _client
          .from('trips')
          .select(
              'id, from_city, to_city, departure_date, departure_time, bookings!inner(id, passenger_id, status, passenger:users!passenger_id(id, full_name))')
          .eq('bookings.status', 'accepted')
          .eq('departure_date', todayStr)
          .gte('departure_time',
              in30Minutes.toIso8601String().split('T')[1].substring(0, 8))
          .lt('departure_time',
              in40Minutes.toIso8601String().split('T')[1].substring(0, 8));

      for (final tripData in response) {
        final trip = TripModel.fromJson(tripData);
        final bookings = tripData['bookings'] as List;

        for (final bookingData in bookings) {
          final passengerId = bookingData['passenger_id'] as String;
          final passengerName =
              bookingData['passenger']?['full_name'] as String? ?? 'مسافر';

          final notification = NotificationModel.createTripReminder(
            userId: passengerId,
            tripId: trip.id,
            tripDestination: trip.toCity,
            departureTime: DateTime.parse(
                '${trip.departureDate.toIso8601String().split('T')[0]} ${trip.departureTime}'),
            reminderType: '30min',
          );

          await NotificationService.createNotification(notification);
        }
      }

      if (kDebugMode) {
        print('✅ 30-minute reminders sent for ${response.length} trips');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending 30-minute reminders: $e');
      }
    }
  }

  // Get upcoming trips for a user
  static Future<List<TripModel>> getUpcomingTrips(String userId) async {
    try {
      final now = DateTime.now();
      final today = DateTime(now.year, now.month, now.day);

      final response = await _client
          .from('trips')
          .select('*, bookings!inner(status), leader:users!leader_id(*)')
          .or('leader_id.eq.$userId,bookings.passenger_id.eq.$userId')
          .eq('bookings.status', 'accepted')
          .gte('departure_date', today.toIso8601String().split('T')[0])
          .order('departure_date', ascending: true)
          .order('departure_time', ascending: true);

      return response
          .map<TripModel>((json) => TripModel.fromJson(json))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching upcoming trips: $e');
      }
      return [];
    }
  }

  // Get trip countdown information
  static Map<String, dynamic> getTripCountdown(TripModel trip) {
    final now = DateTime.now();
    final departureDateTime = DateTime.parse(
        '${trip.departureDate.toIso8601String().split('T')[0]} ${trip.departureTime}');

    final difference = departureDateTime.difference(now);

    if (difference.isNegative) {
      return {
        'status': 'past',
        'message': 'انتهت الرحلة',
        'timeLeft': Duration.zero,
      };
    }

    if (difference.inDays > 0) {
      return {
        'status': 'days',
        'message': 'باقي ${difference.inDays} يوم',
        'timeLeft': difference,
        'urgent': difference.inDays <= 1,
      };
    }

    if (difference.inHours > 0) {
      return {
        'status': 'hours',
        'message': 'باقي ${difference.inHours} ساعة',
        'timeLeft': difference,
        'urgent': difference.inHours <= 2,
      };
    }

    if (difference.inMinutes > 0) {
      return {
        'status': 'minutes',
        'message': 'باقي ${difference.inMinutes} دقيقة',
        'timeLeft': difference,
        'urgent': true,
      };
    }

    return {
      'status': 'now',
      'message': 'الآن',
      'timeLeft': difference,
      'urgent': true,
    };
  }

  // Mark trip as completed
  static Future<Map<String, dynamic>> completeTrip(String tripId) async {
    try {
      await _client
          .from('trips')
          .update({'status': 'completed'}).eq('id', tripId);

      // Update all accepted bookings to completed
      await _client
          .from('bookings')
          .update({
            'status': 'completed',
            'completed_at': DateTime.now().toIso8601String(),
          })
          .eq('trip_id', tripId)
          .eq('status', 'accepted');

      if (kDebugMode) {
        print('✅ Trip marked as completed: $tripId');
      }

      return {
        'success': true,
        'message': 'تم تحديد الرحلة كمكتملة',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error completing trip: $e');
      }
      return {
        'success': false,
        'error': 'حدث خطأ أثناء إكمال الرحلة: $e',
      };
    }
  }

  // Send arrival confirmation notification
  static Future<void> sendArrivalConfirmation(
      String tripId, String userId) async {
    try {
      // Get trip details
      final tripResponse = await _client
          .from('trips')
          .select('*, leader:users!leader_id(*)')
          .eq('id', tripId)
          .single();

      final trip = TripModel.fromJson(tripResponse);

      // Send notification to all passengers
      final bookingsResponse = await _client
          .from('bookings')
          .select('passenger_id')
          .eq('trip_id', tripId)
          .eq('status', 'accepted');

      for (final booking in bookingsResponse) {
        final passengerId = booking['passenger_id'] as String;

        await NotificationService.sendNotification(
          userId: passengerId,
          title: 'تأكيد الوصول',
          body: 'تم تأكيد وصول الرحلة إلى ${trip.toCity}',
          notificationType: 'trip_update',
          tripId: tripId,
          relatedUserId: userId,
        );
      }

      if (kDebugMode) {
        print('✅ Arrival confirmation sent for trip: $tripId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending arrival confirmation: $e');
      }
    }
  }

  // Schedule automatic reminders (this would typically be called by a background service)
  static Future<void> scheduleReminders() async {
    // In a real app, this would set up periodic background tasks
    // For now, we'll just call the reminder function
    await sendTripReminders();
  }
}
