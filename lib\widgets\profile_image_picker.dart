import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
// import 'package:file_picker/file_picker.dart';  // Temporarily disabled
import '../constants/app_theme.dart';
import '../services/profile_image_service.dart';
import '../widgets/safe_network_image.dart';

/// Professional profile image picker widget with upload functionality
/// Features:
/// - Circular profile image with shadow
/// - Loading indicator during upload
/// - Tap to change image
/// - Cross-platform support (web/mobile)
/// - Error handling and user feedback
class ProfileImagePicker extends StatefulWidget {
  final String userId;
  final String? initialImageUrl;
  final double radius;
  final bool showEditIcon;
  final VoidCallback? onImageChanged;
  final Function(String?)? onImageUrlChanged;

  const ProfileImagePicker({
    super.key,
    required this.userId,
    this.initialImageUrl,
    this.radius = 50,
    this.showEditIcon = true,
    this.onImageChanged,
    this.onImageUrlChanged,
  });

  @override
  State<ProfileImagePicker> createState() => _ProfileImagePickerState();
}

class _ProfileImagePickerState extends State<ProfileImagePicker>
    with SingleTickerProviderStateMixin {
  String? _currentImageUrl;
  bool _isUploading = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _currentImageUrl = widget.initialImageUrl;

    // Setup animation for upload feedback
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 0.95,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(ProfileImagePicker oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialImageUrl != oldWidget.initialImageUrl) {
      setState(() {
        _currentImageUrl = widget.initialImageUrl;
      });
    }
  }

  Future<void> _pickAndUploadImage(ImageSource source) async {
    try {
      XFile? pickedFile;

      if (kIsWeb) {
        // Web platform: use image picker (FilePicker disabled)
        pickedFile = await ImagePicker().pickImage(
          source: source,
          maxWidth: 512,
          maxHeight: 512,
          imageQuality: 85,
        );
      } else {
        // Mobile platform: use image picker
        pickedFile = await ImagePicker().pickImage(
          source: source,
          maxWidth: 512,
          maxHeight: 512,
          imageQuality: 85,
        );
      }

      if (pickedFile != null) {
        await _uploadImage(pickedFile);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error picking image: $e');
      }
      _showErrorSnackBar('فشل في اختيار الصورة');
    }
  }

  Future<void> _uploadImage(XFile imageFile) async {
    setState(() {
      _isUploading = true;
    });

    // Animate button press
    _animationController.forward().then((_) {
      _animationController.reverse();
    });

    // Haptic feedback
    HapticFeedback.lightImpact();

    try {
      // Upload image using the professional service
      final imageUrl = await ProfileImageService.uploadAndUpdateProfileImage(
        imageFile: imageFile,
        userId: widget.userId,
      );

      if (imageUrl != null) {
        setState(() {
          _currentImageUrl = imageUrl;
        });

        // Notify parent widget
        widget.onImageChanged?.call();
        widget.onImageUrlChanged?.call(imageUrl);

        _showSuccessSnackBar('تم تحديث الصورة بنجاح!');
      } else {
        _showErrorSnackBar('فشل في رفع الصورة');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }
      _showErrorSnackBar('حدث خطأ أثناء رفع الصورة');
    } finally {
      if (mounted) {
        setState(() {
          _isUploading = false;
        });
      }
    }
  }

  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 20),
              // Handle bar
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),
              const Text(
                'اختر مصدر الصورة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 20),
              // Camera option (mobile only)
              if (!kIsWeb) ...[
                ListTile(
                  leading:
                      const Icon(Icons.camera_alt, color: AppColors.primary),
                  title: const Text('التقاط صورة'),
                  onTap: () {
                    Navigator.pop(context);
                    _pickAndUploadImage(ImageSource.camera);
                  },
                ),
              ],
              // Gallery option
              ListTile(
                leading:
                    const Icon(Icons.photo_library, color: AppColors.secondary),
                title: const Text('اختيار من المعرض'),
                onTap: () {
                  Navigator.pop(context);
                  _pickAndUploadImage(ImageSource.gallery);
                },
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: _isUploading ? null : _showImageSourceDialog,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Stack(
              children: [
                // Main profile image container
                Container(
                  width: widget.radius * 2,
                  height: widget.radius * 2,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.white,
                      width: 3,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.15),
                        blurRadius: 12,
                        offset: const Offset(0, 6),
                        spreadRadius: 2,
                      ),
                      BoxShadow(
                        color: AppColors.primary.withValues(alpha: 0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: widget.radius - 3,
                    backgroundColor: Colors.grey[100],
                    child: _isUploading
                        ? SizedBox(
                            width: widget.radius * 0.6,
                            height: widget.radius * 0.6,
                            child: const CircularProgressIndicator(
                              strokeWidth: 3,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppColors.primary,
                              ),
                            ),
                          )
                        : _currentImageUrl != null
                            ? ClipOval(
                                child: SafeNetworkImage(
                                  imageUrl: _currentImageUrl!,
                                  width: (widget.radius - 3) * 2,
                                  height: (widget.radius - 3) * 2,
                                  fit: BoxFit.cover,
                                ),
                              )
                            : Icon(
                                Icons.person,
                                size: widget.radius * 0.8,
                                color: AppColors.primary.withValues(alpha: 0.7),
                              ),
                  ),
                ),
                // Edit icon
                if (widget.showEditIcon && !_isUploading)
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: Container(
                      width: widget.radius * 0.35,
                      height: widget.radius * 0.35,
                      decoration: BoxDecoration(
                        color: AppColors.accent,
                        shape: BoxShape.circle,
                        border: Border.all(color: Colors.white, width: 2),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.edit,
                        size: widget.radius * 0.2,
                        color: Colors.white,
                      ),
                    ),
                  ),
              ],
            ),
          );
        },
      ),
    );
  }
}
