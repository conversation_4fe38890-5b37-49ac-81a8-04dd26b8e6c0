import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/booking.dart';

class BookingService {
  final SupabaseClient _supabase = Supabase.instance.client;

  Future<List<Booking>> fetchPendingBookings(String driverId) async {
    try {
      print('Fetching pending bookings...');
      final response = await _supabase
          .from('bookings')
          .select()
          .eq('driver_id', driverId)
          .eq('status', 'pending');
      
      if (response == null) {
        return [];
      }
      
      return List<Booking>.from(response.map((x) => Booking.fromJson(x)));
    } catch (e) {
      print('Error fetching pending bookings: $e');
      return [];
    }
  }
}
import 'package:get/get.dart';
import '../models/booking.dart';
import '../services/booking_service.dart';

class BookingController extends GetxController {
  final BookingService _bookingService = Get.find<BookingService>();
  final RxList<Booking> pendingBookings = <Booking>[].obs;
  final RxBool isLoading = false.obs;

  Future<void> fetchPendingBookings(String driverId) async {
    isLoading.value = true;
    try {
      final bookings = await _bookingService.fetchPendingBookings(driverId);
      pendingBookings.value = bookings;
    } finally {
      isLoading.value = false;
    }
  }
}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../controllers/booking_controller.dart';
import '../models/booking.dart';
import 'booking_request_card.dart';

class RequestsTab extends StatelessWidget {
  final String driverId;

  const RequestsTab({Key? key, required this.driverId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final BookingController bookingController = Get.put(BookingController());

    // 当标签页被打开时获取待处理的预订
    WidgetsBinding.instance?.addPostFrameCallback((_) {
      bookingController.fetchPendingBookings(driverId);
    });

    return Obx(() {
      if (bookingController.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }
      
      if (bookingController.pendingBookings.isEmpty) {
        return const Center(child: Text('No pending requests'));
      }
      
      return ListView.builder(
        itemCount: bookingController.pendingBookings.length,
        itemBuilder: (context, index) {
          final booking = bookingController.pendingBookings[index];
          return BookingRequestCard(booking: booking);
        },
      );
    });
  }
}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:safarni/controllers/booking_controller.dart';
import 'package:safarni/screens/tabs/requests_tab.dart';

class DriverDashboard extends StatelessWidget {
  final String driverId;

  const DriverDashboard({Key? key, required this.driverId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BookingController>(
      init: BookingController(),
      builder: (controller) {
        return DefaultTabController(
          length: 3, // 假设有3个标签页
          child: Scaffold(
            appBar: AppBar(
              bottom: const TabBar(
                tabs: [
                  Tab(text: 'Requests'),
                  Tab(text: 'Active'),
                  Tab(text: 'History'),
                ],
              ),
            ),
            body: TabBarView(
              children: [
                RequestsTab(driverId: driverId),
                // 其他标签页...
              ],
            ),
          ),
        );
      },
    );
  }
}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/booking_controller.dart';
import '../../models/booking_model.dart';
import 'booking_request_card.dart';

class RequestsTab extends StatelessWidget {
  final String driverId;

  const RequestsTab({Key? key, required this.driverId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final BookingController bookingController = Get.put(BookingController());

    // جلب الحجوزات عند فتح التبويب
    WidgetsBinding.instance?.addPostFrameCallback((_) {
      bookingController.fetchPendingBookings(driverId);
    });

    return Obx(() {
      if (bookingController.isLoading.value) {
        return const Center(child: CircularProgressIndicator());
      }
      
      if (bookingController.pendingBookings.isEmpty) {
        return const Center(child: Text('لا توجد طلبات معلقة'));
      }
      
      return ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: bookingController.pendingBookings.length,
        itemBuilder: (context, index) {
          final booking = bookingController.pendingBookings[index];
          return BookingRequestCard(booking: booking);
        },
      );
    });
  }
}
import 'package:flutter/material.dart';
import '../../models/booking_model.dart';

class BookingRequestCard extends StatelessWidget {
  final BookingModel booking;

  const BookingRequestCard({Key? key, required this.booking}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final passengerName = booking.passengerDetails?['name'] ?? 'غير محدد';
    final passengerPhone = booking.passengerDetails?['phone'] ?? 'غير محدد';
    
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'طلب حجز جديد',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text('الركاب: ${booking.seatsBooked} مقعد'),
            const SizedBox(height: 4),
            Text('الركاب: $passengerName'),
            const SizedBox(height: 4),
            Text('الهاتف: $passengerPhone'),
            if (booking.message != null && booking.message!.isNotEmpty) ...[
              const SizedBox(height: 4),
              Text('الرسالة: ${booking.message}'),
            ],
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                ElevatedButton(
                  onPressed: () {
                    // TODO: تنفيذ رفض الحجز
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    foregroundColor: Colors.white,
                  ),
                  child: const Text('رفض'),
                ),
                const SizedBox(width: 8),
                ElevatedButton(
                  onPressed: () {
                    // TODO: تنفيذ قبول الحجز
                  },
                  child: const Text('قبول'),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../controllers/booking_controller.dart';
import 'tabs/requests_tab.dart';

class DriverDashboard extends StatelessWidget {
  final String driverId;

  const DriverDashboard({Key? key, required this.driverId}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GetBuilder<BookingController>(
      init: BookingController(),
      builder: (controller) {
        return DefaultTabController(
          length: 3, // عدد التبويبات
          child: Scaffold(
            appBar: AppBar(
              title: const Text('لوحة التحكم للسائق'),
              bottom: const TabBar(
                tabs: [
                  Tab(text: 'الطلبات'),
                  Tab(text: 'الرحلات النشطة'),
                  Tab(text: 'السجل'),
                ],
              ),
            ),
            body: TabBarView(
              children: [
                RequestsTab(driverId: driverId),
                // باقي التبويبات...
              ],
            ),
          ),
        );
      },
    );
  }
}
flutter run -d chrome
import 'package:supabase_flutter/supabase_flutter.dart';
import 'booking_model.dart'; // تأكد من استيراد نموذج الحجز

class BookingService {
  final SupabaseClient supabase;

  BookingService(this.supabase);

  Future<List<BookingModel>> getDriverPendingBookings(String driverId) async {
    print("Fetching pending bookings for driver: $driverId");
    final response = await supabase
        .from('bookings')
        .select()
        .match({'driver_id': driverId, 'status': 'pending'})
        .order('created_at', ascending: false)
        .execute(); // تأكد من استخدام execute لجلب البيانات

    if (response.error != null) {
      print("Error fetching bookings: ${response.error!.message}");
      return [];
    }

    final List<dynamic> data = response.data as List<dynamic>;
    if (data.isEmpty) {
      print("No pending bookings found.");
      return [];
    }

    print("Retrieved ${data.length} pending bookings");
    return data.map((b) => BookingModel.fromJson(b)).toList();
  }
}
import 'package:flutter/material.dart';
import 'booking_service.dart';
import 'booking_model.dart';

class PendingBookingsPage extends StatefulWidget {
  @override
  _PendingBookingsPageState createState() => _PendingBookingsPageState();
}

class _PendingBookingsPageState extends State<PendingBookingsPage> {
  List<BookingModel> pendingBookings = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    fetchPendingBookings();
  }

  Future<void> fetchPendingBookings() async {
    final driverId = currentUser.id; // تأكد من الحصول على معرف القائد الحالي
    final bookingService = BookingService(supabase); // تأكد من تهيئة SupabaseClient
    pendingBookings = await bookingService.getDriverPendingBookings(driverId);
    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Center(child: CircularProgressIndicator());
    }

    if (pendingBookings.isEmpty) {
      return Center(child: Text("لا توجد طلبات معلقة"));
    }

    return ListView.builder(
      itemCount: pendingBookings.length,
      itemBuilder: (context, index) {
        final booking = pendingBookings[index];
        return Card(
          child: ListTile(
            title: Text("حجز رقم: ${booking.id}"),
            subtitle: Text("الراكب: ${booking.passengerDetails['name']}"),
            // أضف المزيد من التفاصيل حسب الحاجة
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: Icon(Icons.check),
                  onPressed: () {
                    // منطق قبول الحجز
                  },
                ),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () {
                    // منطق رفض الحجز
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
import 'package:flutter/material.dart';
import 'booking_service.dart';
import 'booking_model.dart';

class PendingBookingsPage extends StatefulWidget {
  @override
  _PendingBookingsPageState createState() => _PendingBookingsPageState();
}

class _PendingBookingsPageState extends State<PendingBookingsPage> {
  List<BookingModel> pendingBookings = [];
  bool isLoading = true;

  @override
  void initState() {
    super.initState();
    fetchPendingBookings();
  }

  Future<void> fetchPendingBookings() async {
    final driverId = currentUser.id; // تأكد من الحصول على معرف القائد الحالي
    final bookingService = BookingService(supabase); // تأكد من تهيئة SupabaseClient
    pendingBookings = await bookingService.getDriverPendingBookings(driverId);
    setState(() {
      isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    if (isLoading) {
      return Center(child: CircularProgressIndicator());
    }

    if (pendingBookings.isEmpty) {
      return Center(child: Text("لا توجد طلبات معلقة"));
    }

    return ListView.builder(
      itemCount: pendingBookings.length,
      itemBuilder: (context, index) {
        final booking = pendingBookings[index];
        return Card(
          child: ListTile(
            title: Text("حجز رقم: ${booking.id}"),
            subtitle: Text("الراكب: ${booking.passengerDetails['name']}"),
            // أضف المزيد من التفاصيل حسب الحاجة
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: Icon(Icons.check),
                  onPressed: () {
                    // منطق قبول الحجز
                  },
                ),
                IconButton(
                  icon: Icon(Icons.close),
                  onPressed: () {
                    // منطق رفض الحجز
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
// import 'package:image_picker/image_picker.dart';  // Commented out for web compatibility
import 'package:file_picker/file_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

class StorageService {
  static final SupabaseClient _supabase = Supabase.instance.client;
  static const String _driverLicensesBucket =
      'driver-licenses'; // Private bucket
  static const String _profileImagesBucket = 'profile-images'; // Public bucket
  static const String _tripGalleryBucket = 'trip_gallery';

  /// Upload profile image to public bucket
  static Future<String?> uploadProfileImage({
    required PlatformFile imageFile,
    required String userId,
  }) async {
    try {
      // Get current user to ensure authentication
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('Profile upload failed: User not authenticated');
        }
        return null;
      }

      // Security check: user can only upload their own profile
      if (currentUser.id != userId) {
        if (kDebugMode) {
          print('Profile upload failed: User ID mismatch');
        }
        return null;
      }

      // Force .jpeg extension for consistency
      final fileName = 'profile_$userId.jpeg';
      final fullPath = 'users/$fileName';

      if (kDebugMode) {
        print('Profile upload path: $fullPath');
        print('Bucket: $_profileImagesBucket (public)');
      }

      // Read image bytes
      final Uint8List imageBytes = imageFile.bytes ?? Uint8List(0);

      // Upload to public bucket with .jpeg format
      final uploadOptions = FileOptions(
        cacheControl: '3600',
        upsert: true,
        contentType: 'image/jpeg',
      );

      final response = await _supabase.storage
          .from(_profileImagesBucket)
          .uploadBinary(fullPath, imageBytes, fileOptions: uploadOptions);

      if (response.isNotEmpty) {
        // Generate public URL
        final publicUrl =
            _supabase.storage.from(_profileImagesBucket).getPublicUrl(fullPath);

        if (kDebugMode) {
          print('Profile image uploaded successfully!');
          print('Public URL: $publicUrl');
        }

        return publicUrl;
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading profile image: $e');
      }
      // Return null for any upload error
      return null;
    }
  }

  /// Upload profile image to the existing profile-images bucket
  /// Uses path: profile-images/users/{user_id}.jpg
  static Future<String?> uploadUserProfileImage({
    required PlatformFile imageFile,
    required String userId,
  }) async {
    try {
      // Get current user to ensure authentication
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('Profile upload failed: User not authenticated');
        }
        return null;
      }

      // Security check: user can only upload their own profile
      if (currentUser.id != userId) {
        if (kDebugMode) {
          print('Profile upload failed: User ID mismatch');
        }
        return null;
      }

      // Use .jpg extension and proper path structure
      final fileName = '$userId.jpg';
      final fullPath = 'users/$fileName';

      if (kDebugMode) {
        print('Profile upload path: $fullPath');
        print('Bucket: $_profileImagesBucket (public)');
      }

      // Read image bytes
      final Uint8List? imageBytes = imageFile.bytes;
      if (imageBytes == null) {
        if (kDebugMode) {
          print('❌ No image data available');
        }
        return null;
      }

      // Upload to existing profile-images bucket with .jpg format
      const uploadOptions = FileOptions(
        cacheControl: '3600',
        upsert: true,
        contentType: 'image/jpeg',
      );

      final response = await _supabase.storage
          .from(_profileImagesBucket)
          .uploadBinary(fullPath, imageBytes, fileOptions: uploadOptions);

      if (response.isNotEmpty) {
        // Get public URL
        final publicUrl =
            _supabase.storage.from(_profileImagesBucket).getPublicUrl(fullPath);

        if (kDebugMode) {
          print('✅ Profile image uploaded successfully: $publicUrl');
        }

        return publicUrl;
      } else {
        if (kDebugMode) {
          print('❌ Profile upload failed: Empty response');
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Profile upload error: $e');
      }
      return null;
    }
  }

  /// Upload an image to Supabase Storage with proper access control
  /// This method works for both web and mobile platforms
  static Future<String?> uploadImage({
    required PlatformFile imageFile,
    required String bucket,
    String? folder,
    String? customFileName,
    String? userId,
    bool isPrivate = false,
  }) async {
    try {
      // Get current user to ensure authentication
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('Upload failed: User not authenticated');
        }
        return null;
      }

      // Security check: user can only upload their own files when userId is provided
      if (userId != null && currentUser.id != userId) {
        if (kDebugMode) {
          print('Upload failed: User ID mismatch');
        }
        return null;
      }

      // Generate filename
      final uuid = const Uuid();
      final fileExtension = imageFile.name.split('.').last.toLowerCase();
      final fileName = customFileName ?? '${uuid.v4()}.$fileExtension';
      final fullPath = folder != null ? '$folder/$fileName' : fileName;

      // Read image bytes (works for both web and mobile)
      final Uint8List imageBytes = imageFile.bytes ?? Uint8List(0);

      // Determine content type
      String contentType;
      switch (fileExtension) {
        case 'jpg':
        case 'jpeg':
          contentType = 'image/jpeg';
          break;
        case 'png':
          contentType = 'image/png';
          break;
        case 'webp':
          contentType = 'image/webp';
          break;
        default:
          contentType = 'image/jpeg';
      }

      // Prepare upload options
      final uploadOptions = FileOptions(
        cacheControl: '3600',
        upsert: true,
        contentType: contentType,
      );

      if (kDebugMode) {
        print('Uploading to bucket: $bucket, path: $fullPath');
      }

      // Upload to Supabase Storage
      final response = await _supabase.storage
          .from(bucket)
          .uploadBinary(fullPath, imageBytes, fileOptions: uploadOptions);

      if (response.isNotEmpty) {
        // Return the file path for private files, public URL for public files
        if (isPrivate) {
          if (kDebugMode) {
            print('Private file uploaded successfully: $fullPath');
          }
          return fullPath; // Return path for signed URL generation
        } else {
          // Get public URL for public files
          final publicUrl =
              _supabase.storage.from(bucket).getPublicUrl(fullPath);

          if (kDebugMode) {
            print('Public file uploaded successfully: $publicUrl');
          }
          return publicUrl;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
        if (e is StorageException) {
          print('Status code: ${e.statusCode}');
          print('Message: ${e.message}');
        }
      }
      return null;
    }
  }

  /// Upload driver license image to private bucket
  static Future<String?> uploadDriverLicense({
    required PlatformFile imageFile,
    required String userId,
  }) async {
    try {
      // Get current user to ensure authentication
      final currentUser = _supabase.auth.currentUser;
      if (currentUser == null) {
        if (kDebugMode) {
          print('License upload failed: User not authenticated');
        }
        return null;
      }

      // Security check: user can only upload their own license
      if (currentUser.id != userId) {
        if (kDebugMode) {
          print('License upload failed: User ID mismatch');
        }
        return null;
      }

      // Force .jpeg extension for consistency
      final fileName = 'license_$userId.jpeg';
      final fullPath = 'licenses/$fileName';

      if (kDebugMode) {
        print('License upload path: $fullPath');
        print('Bucket: $_driverLicensesBucket (private)');
        print('User ID: ${currentUser.id}');
      }

      // Read image bytes
      final Uint8List imageBytes = imageFile.bytes ?? Uint8List(0);

      if (kDebugMode) {
        print('File size: ${imageBytes.length} bytes');
      }

      // Upload the file with .jpeg format
      final uploadOptions = FileOptions(
        cacheControl: '3600',
        upsert: true,
        contentType: 'image/jpeg',
      );

      if (kDebugMode) {
        print('Starting upload to Supabase...');
      }

      final response = await _supabase.storage
          .from(_driverLicensesBucket)
          .uploadBinary(fullPath, imageBytes, fileOptions: uploadOptions);

      if (response.isEmpty) {
        if (kDebugMode) {
          print('Upload failed: Empty response from Supabase');
        }
        return null;
      }

      if (kDebugMode) {
        print('File uploaded successfully!');
        print('Path: $fullPath');
        print('Driver license upload completed successfully');
      }

      // Return success status - license uploaded to private bucket
      return fullPath;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading driver license: $e');
      }
      // Return null for any upload error
      return null;
    }
  }

  /// Upload car image to public bucket
  static Future<String?> uploadCarImage({
    required PlatformFile imageFile,
    required String userId,
  }) async {
    return await uploadImage(
      imageFile: imageFile,
      bucket: _profileImagesBucket,
      folder: 'vehicles',
      customFileName: 'vehicle_$userId.${imageFile.name.split('.').last}',
      userId: userId,
      isPrivate: false,
    );
  }

  /// Upload trip gallery image
  static Future<String?> uploadTripImage({
    required PlatformFile imageFile,
    required String tripId,
    int? imageIndex,
  }) async {
    final fileName = imageIndex != null
        ? 'trip_${tripId}_$imageIndex.${imageFile.name.split('.').last}'
        : null;

    return await uploadImage(
      imageFile: imageFile,
      bucket: _tripGalleryBucket,
      folder: 'trips',
      customFileName: fileName,
    );
  }

  /// Delete an image from storage
  static Future<bool> deleteImage({
    required String bucket,
    required String filePath,
  }) async {
    try {
      await _supabase.storage.from(bucket).remove([filePath]);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting image: $e');
      }
      return false;
    }
  }

  /// Get file path from public URL
  static String? getFilePathFromUrl(String publicUrl, String bucket) {
    try {
      final uri = Uri.parse(publicUrl);
      final pathSegments = uri.pathSegments;
      final bucketIndex = pathSegments.indexOf(bucket);

      if (bucketIndex != -1 && bucketIndex < pathSegments.length - 1) {
        return pathSegments.sublist(bucketIndex + 1).join('/');
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing URL: $e');
      }
      return null;
    }
  }

  /// Get profile image public URL for a user
  static String? getProfileImageUrl(String userId, {String? storedUrl}) {
    try {
      // If we have a stored URL, use it
      if (storedUrl != null && storedUrl.isNotEmpty) {
        return storedUrl;
      }

      // Generate public URL for profile image using .jpeg extension exactly
      final filePath = 'users/profile_$userId.jpeg';
      final url =
          _supabase.storage.from(_profileImagesBucket).getPublicUrl(filePath);

      if (kDebugMode) {
        print('Generated profile URL: $url');
      }
      return url;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting profile image URL: $e');
      }
      return null;
    }
  }

  /// Generate public URL for a specific profile image path
  static String getProfileImagePublicUrl(String userId) {
    final filePath = 'users/profile_$userId.jpeg';
    return _supabase.storage.from(_profileImagesBucket).getPublicUrl(filePath);
  }

  /// Get profile image URL with optional delay for newly uploaded images
  static Future<String?> getProfileImageUrlWithDelay(
    String userId, {
    String? storedUrl,
    Duration delay = const Duration(seconds: 1),
  }) async {
    // If we have a stored URL, use it immediately
    if (storedUrl != null && storedUrl.isNotEmpty) {
      return storedUrl;
    }

    // Wait for the specified delay to ensure public availability
    await Future.delayed(delay);

    // Generate the URL
    return getProfileImageUrl(userId, storedUrl: storedUrl);
  }

  /// Get car image URL for a user (using public profile-images bucket)
  static String? getCarImageUrl(String userId) {
    try {
      // Try different file extensions in profile-images/vehicles/
      final extensions = ['jpg', 'jpeg', 'png', 'webp'];
      for (final ext in extensions) {
        final filePath = 'vehicles/vehicle_$userId.$ext';
        final url =
            _supabase.storage.from(_profileImagesBucket).getPublicUrl(filePath);

        if (url.isNotEmpty) {
          return url;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting car image URL: $e');
      }
      return null;
    }
  }

  /// Check if an image exists in storage
  static Future<bool> imageExists({
    required String bucket,
    required String filePath,
  }) async {
    try {
      final pathParts = filePath.split('/');
      final folder = pathParts.length > 1 ? pathParts.first : '';
      final fileName = pathParts.last;

      final response = await _supabase.storage.from(bucket).list(path: folder);

      return response.any((file) => file.name == fileName);
    } catch (e) {
      if (kDebugMode) {
        print('Error checking image existence: $e');
      }
      return false;
    }
  }

  /// Check if storage buckets exist and are accessible
  static Future<bool> checkStorageHealth() async {
    try {
      // Try to list files in each bucket to verify access
      await _supabase.storage.from(_driverLicensesBucket).list();
      await _supabase.storage.from(_profileImagesBucket).list();
      await _supabase.storage.from(_tripGalleryBucket).list();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Storage health check failed: $e');
      }
      return false;
    }
  }
}
