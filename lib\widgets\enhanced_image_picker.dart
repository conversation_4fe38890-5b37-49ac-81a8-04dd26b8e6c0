import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:image_picker/image_picker.dart';
// import 'package:file_picker/file_picker.dart';  // Temporarily disabled
import '../constants/app_theme.dart';
import '../services/storage_service.dart';

/// Enhanced dual-source image picker with camera and gallery options
class EnhancedImagePicker extends StatefulWidget {
  final String userId;
  final String? initialImageUrl;
  final double radius;
  final bool showEditIcon;
  final VoidCallback? onImageChanged;
  final Function(String?)? onImageUrlChanged;
  final Function(XFile?)? onImageSelected;
  final bool autoUpload;
  final String? placeholder;

  const EnhancedImagePicker({
    super.key,
    required this.userId,
    this.initialImageUrl,
    this.radius = 50,
    this.showEditIcon = true,
    this.onImageChanged,
    this.onImageUrlChanged,
    this.onImageSelected,
    this.autoUpload = true,
    this.placeholder,
  });

  @override
  State<EnhancedImagePicker> createState() => _EnhancedImagePickerState();
}

class _EnhancedImagePickerState extends State<EnhancedImagePicker>
    with TickerProviderStateMixin {
  bool _isUploading = false;
  bool _isSelecting = false;
  String? _currentImageUrl;
  XFile? _selectedImage;
  late AnimationController _scaleController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    _currentImageUrl = widget.initialImageUrl;

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _scaleController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _scaleController.dispose();
    super.dispose();
  }

  Future<void> _showImageSourceDialog() async {
    HapticFeedback.lightImpact();

    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      isScrollControlled: true,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const SizedBox(height: 20),
              // Handle bar
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),
              const Text(
                'اختر مصدر الصورة',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppColors.textPrimary,
                ),
              ),
              const SizedBox(height: 20),

              // Camera option (mobile only)
              if (!kIsWeb) ...[
                _buildSourceOption(
                  icon: Icons.camera_alt,
                  title: 'التقاط صورة',
                  subtitle: 'استخدم الكاميرا لالتقاط صورة جديدة',
                  onTap: () {
                    Navigator.pop(context);
                    _pickImage(ImageSource.camera);
                  },
                ),
                const Divider(height: 1),
              ],

              // Gallery option
              _buildSourceOption(
                icon: Icons.photo_library,
                title: kIsWeb ? 'اختر من الجهاز' : 'اختر من المعرض',
                subtitle: kIsWeb
                    ? 'اختر صورة من ملفات الجهاز'
                    : 'اختر صورة من معرض الصور',
                onTap: () {
                  Navigator.pop(context);
                  _pickImage(ImageSource.gallery);
                },
              ),

              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSourceOption({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Container(
        width: 48,
        height: 48,
        decoration: BoxDecoration(
          color: AppColors.primary.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Icon(icon, color: AppColors.primary, size: 24),
      ),
      title: Text(
        title,
        style: const TextStyle(
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: const TextStyle(
          color: AppColors.textSecondary,
          fontSize: 12,
        ),
      ),
      onTap: onTap,
    );
  }

  Future<void> _pickImage(ImageSource source) async {
    setState(() => _isSelecting = true);

    try {
      XFile? pickedFile;

      if (kIsWeb) {
        // Web: Use ImagePicker (FilePicker disabled)
        pickedFile = await ImagePicker().pickImage(
          source: source,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 90,
        );

        // Check file size for web
        if (pickedFile != null) {
          final bytes = await pickedFile.readAsBytes();
          if (bytes.length > 5 * 1024 * 1024) {
            throw Exception(
                'حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
          }
        }
      } else {
        // Mobile: Use ImagePicker
        pickedFile = await ImagePicker().pickImage(
          source: source,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 90,
        );

        // Check file size for mobile
        if (pickedFile != null) {
          final bytes = await pickedFile.readAsBytes();
          if (bytes.length > 5 * 1024 * 1024) {
            throw Exception(
                'حجم الصورة كبير جداً. يرجى اختيار صورة أصغر من 5 ميجابايت');
          }
        }
      }

      if (pickedFile != null) {
        setState(() {
          _selectedImage = pickedFile;
          _isSelecting = false;
        });

        // Notify parent about image selection
        widget.onImageSelected?.call(pickedFile);

        if (widget.autoUpload) {
          await _uploadImage(pickedFile);
        } else {
          // If not auto-uploading, just notify about the change
          widget.onImageChanged?.call();
        }

        HapticFeedback.mediumImpact();
      } else {
        setState(() => _isSelecting = false);
      }
    } catch (e) {
      setState(() => _isSelecting = false);

      if (kDebugMode) {
        print('Error picking image: $e');
      }

      _showErrorSnackBar(e.toString().replaceAll('Exception: ', ''));
    }
  }

  String _getMimeType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'webp':
        return 'image/webp';
      default:
        return 'image/jpeg';
    }
  }

  Future<void> _uploadImage(XFile imageFile) async {
    setState(() => _isUploading = true);

    try {
      final imageUrl = await StorageService.uploadUserProfileImage(
        imageFile: imageFile,
        userId: widget.userId,
      );

      if (imageUrl != null) {
        setState(() {
          _currentImageUrl = imageUrl;
          _isUploading = false;
        });

        widget.onImageUrlChanged?.call(imageUrl);
        widget.onImageChanged?.call();

        _showSuccessSnackBar('تم رفع الصورة بنجاح!');
      } else {
        throw Exception('فشل في رفع الصورة');
      }
    } catch (e) {
      setState(() => _isUploading = false);

      if (kDebugMode) {
        print('Error uploading image: $e');
      }

      _showErrorSnackBar('فشل في رفع الصورة');
    }
  }

  void _showErrorSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
    }
  }

  void _showSuccessSnackBar(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.success,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _scaleController.forward(),
      onTapUp: (_) => _scaleController.reverse(),
      onTapCancel: () => _scaleController.reverse(),
      onTap: widget.showEditIcon ? _showImageSourceDialog : null,
      child: AnimatedBuilder(
        animation: _scaleAnimation,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Container(
              width: widget.radius * 2,
              height: widget.radius * 2,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withValues(alpha: 0.2),
                    blurRadius: 15,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: Stack(
                children: [
                  // Main avatar
                  Container(
                    width: widget.radius * 2,
                    height: widget.radius * 2,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(
                        color: AppColors.primary.withValues(alpha: 0.3),
                        width: 3,
                      ),
                    ),
                    child: ClipOval(
                      child: _buildImageContent(),
                    ),
                  ),

                  // Loading overlay
                  if (_isUploading || _isSelecting)
                    Container(
                      width: widget.radius * 2,
                      height: widget.radius * 2,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: Colors.black.withValues(alpha: 0.5),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const CircularProgressIndicator(
                              valueColor:
                                  AlwaysStoppedAnimation<Color>(Colors.white),
                              strokeWidth: 2,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _isUploading
                                  ? 'جاري الرفع...'
                                  : 'جاري التحديد...',
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),

                  // Edit icon
                  if (widget.showEditIcon && !_isUploading && !_isSelecting)
                    Positioned(
                      bottom: 0,
                      right: 0,
                      child: Container(
                        width: widget.radius * 0.6,
                        height: widget.radius * 0.6,
                        decoration: BoxDecoration(
                          color: AppColors.primary,
                          shape: BoxShape.circle,
                          border: Border.all(color: Colors.white, width: 2),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 8,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.camera_alt,
                          color: Colors.white,
                          size: widget.radius * 0.3,
                        ),
                      ),
                    ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildImageContent() {
    // Show selected image (not yet uploaded)
    if (_selectedImage != null && !widget.autoUpload) {
      return kIsWeb
          ? FutureBuilder<Uint8List>(
              future: _selectedImage!.readAsBytes(),
              builder: (context, snapshot) {
                if (snapshot.hasData) {
                  return Image.memory(
                    snapshot.data!,
                    width: widget.radius * 2,
                    height: widget.radius * 2,
                    fit: BoxFit.cover,
                  );
                }
                return _buildPlaceholder();
              },
            )
          : Image.file(
              File(_selectedImage!.path),
              width: widget.radius * 2,
              height: widget.radius * 2,
              fit: BoxFit.cover,
            );
    }

    // Show uploaded image URL
    if (_currentImageUrl != null && _currentImageUrl!.isNotEmpty) {
      return Image.network(
        _currentImageUrl!,
        width: widget.radius * 2,
        height: widget.radius * 2,
        fit: BoxFit.cover,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return _buildPlaceholder();
        },
        errorBuilder: (context, error, stackTrace) {
          return _buildPlaceholder();
        },
      );
    }

    // Show placeholder
    return _buildPlaceholder();
  }

  Widget _buildPlaceholder() {
    return Container(
      width: widget.radius * 2,
      height: widget.radius * 2,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: LinearGradient(
          colors: [
            AppColors.primary.withValues(alpha: 0.1),
            AppColors.secondary.withValues(alpha: 0.1),
          ],
        ),
      ),
      child: Center(
        child: widget.placeholder != null
            ? Text(
                widget.placeholder!,
                style: TextStyle(
                  color: AppColors.primary,
                  fontSize: widget.radius * 0.4,
                  fontWeight: FontWeight.bold,
                ),
              )
            : Icon(
                Icons.person,
                size: widget.radius * 0.8,
                color: AppColors.primary.withValues(alpha: 0.5),
              ),
      ),
    );
  }
}
