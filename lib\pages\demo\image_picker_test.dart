import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
// import 'package:file_picker/file_picker.dart';  // Temporarily disabled
import '../../constants/app_theme.dart';

class ImagePickerTest extends StatefulWidget {
  const ImagePickerTest({super.key});

  @override
  State<ImagePickerTest> createState() => _ImagePickerTestState();
}

class _ImagePickerTestState extends State<ImagePickerTest> {
  XFile? _selectedImage;
  bool _isLoading = false;
  String? _error;

  Future<void> _testImagePicker() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      XFile? pickedFile;

      if (kIsWeb) {
        // Web: FilePicker is disabled, use ImagePicker instead
        pickedFile = await ImagePicker().pickImage(
          source: ImageSource.gallery,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 90,
        );

        if (kDebugMode) {
          print('🌐 Web platform using ImagePicker as fallback');
        }
      } else {
        // Mobile: Use ImagePicker
        pickedFile = await ImagePicker().pickImage(
          source: ImageSource.gallery,
          maxWidth: 1024,
          maxHeight: 1024,
          imageQuality: 90,
        );

        // Check file size for mobile
        if (pickedFile != null) {
          final bytes = await pickedFile.readAsBytes();
          if (bytes.length > 5 * 1024 * 1024) {
            throw Exception(
                'File too large. Please select an image smaller than 5MB.');
          }
        }
      }

      if (pickedFile != null) {
        if (kDebugMode) {
          print('✅ Image selected successfully: ${pickedFile.name}');
          print(
              '📏 File size: ${(await pickedFile.readAsBytes()).length} bytes');
          print('🎯 MIME type: ${pickedFile.mimeType ?? 'unknown'}');
        }

        setState(() {
          _selectedImage = pickedFile;
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = e.toString();
      });

      if (kDebugMode) {
        print('❌ Image picker error: $e');
      }
    }
  }

  String _getMimeType(String fileName) {
    final extension = fileName.split('.').last.toLowerCase();
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'image/jpeg';
      case 'png':
        return 'image/png';
      case 'webp':
        return 'image/webp';
      case 'gif':
        return 'image/gif';
      default:
        return 'image/jpeg';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Image Picker Test'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Image preview
            Container(
              width: 200,
              height: 200,
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.border, width: 2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: _selectedImage != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: kIsWeb
                          ? FutureBuilder<Uint8List>(
                              future: _selectedImage!.readAsBytes(),
                              builder: (context, snapshot) {
                                if (snapshot.hasData) {
                                  return Image.memory(
                                    snapshot.data!,
                                    width: 196,
                                    height: 196,
                                    fit: BoxFit.cover,
                                  );
                                }
                                return const Center(
                                    child: CircularProgressIndicator());
                              },
                            )
                          : FutureBuilder<Uint8List>(
                              future: _selectedImage!.readAsBytes(),
                              builder: (context, snapshot) {
                                if (snapshot.hasData) {
                                  return Image.memory(
                                    snapshot.data!,
                                    width: 196,
                                    height: 196,
                                    fit: BoxFit.cover,
                                  );
                                }
                                return const Center(
                                    child: CircularProgressIndicator());
                              },
                            ),
                    )
                  : const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.image,
                              size: 48, color: AppColors.textSecondary),
                          SizedBox(height: 8),
                          Text('No image selected',
                              style: TextStyle(color: AppColors.textSecondary)),
                        ],
                      ),
                    ),
            ),

            const SizedBox(height: 24),

            // File info
            if (_selectedImage != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('File Name: ${_selectedImage!.name}'),
                      Text(
                          'MIME Type: ${_selectedImage!.mimeType ?? 'unknown'}'),
                      FutureBuilder<Uint8List>(
                        future: _selectedImage!.readAsBytes(),
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            return Text(
                                'File Size: ${(snapshot.data!.length / 1024).toStringAsFixed(1)} KB');
                          }
                          return const Text('File Size: calculating...');
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Error message
            if (_error != null) ...[
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.error.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppColors.error),
                ),
                child: Text(
                  _error!,
                  style: const TextStyle(color: AppColors.error),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Pick image button
            ElevatedButton(
              onPressed: _isLoading ? null : _testImagePicker,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primary,
                foregroundColor: Colors.white,
                padding:
                    const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text('Pick Image'),
            ),

            const SizedBox(height: 16),

            Text(
              kIsWeb
                  ? 'Running on Web - Using ImagePicker (FilePicker disabled)'
                  : 'Running on Mobile - Using ImagePicker',
              style:
                  const TextStyle(color: AppColors.textSecondary, fontSize: 12),
            ),
          ],
        ),
      ),
    );
  }
}
