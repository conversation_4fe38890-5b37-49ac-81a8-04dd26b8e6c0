import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/notification_model.dart';

class NotificationService {
  static final SupabaseClient _client = Supabase.instance.client;
  static final FlutterLocalNotificationsPlugin _localNotifications =
      FlutterLocalNotificationsPlugin();

  static bool _isInitialized = false;

  // Initialize local notifications
  static Future<void> initialize() async {
    if (_isInitialized) return;

    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );

    const initSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Request permissions for Android 13+
    await _requestNotificationPermissions();

    _isInitialized = true;

    if (kDebugMode) {
      print('✅ Local notifications initialized');
    }
  }

  static Future<void> _requestNotificationPermissions() async {
    final androidPlugin = _localNotifications.resolvePlatformSpecificImplementation<
        AndroidFlutterLocalNotificationsPlugin>();

    if (androidPlugin != null) {
      await androidPlugin.requestNotificationsPermission();
    }
  }

  static void _onNotificationTapped(NotificationResponse response) {
    if (kDebugMode) {
      print('🔔 Notification tapped: ${response.payload}');
    }

    // Handle notification tap - navigate to appropriate screen
    // This would typically use a navigation service or global navigator
  }

  // Create a new notification
  static Future<Map<String, dynamic>> createNotification(NotificationModel notification) async {
    try {
      if (kDebugMode) {
        print('🔔 Creating notification for user: ${notification.userId}');
      }

      final response = await _client
          .from('notifications')
          .insert(notification.toJson())
          .select()
          .single();

      final createdNotification = NotificationModel.fromJson(response);

      if (kDebugMode) {
        print('✅ Notification created successfully: ${createdNotification.id}');
      }

      return {
        'success': true,
        'notification': createdNotification,
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating notification: $e');
      }
      return {
        'success': false,
        'error': 'حدث خطأ أثناء إنشاء الإشعار: $e',
      };
    }
  }

  // Get notifications for a user
  static Future<List<NotificationModel>> getUserNotifications(String userId, {
    int limit = 50,
    bool unreadOnly = false,
  }) async {
    try {
      dynamic query = _client
          .from('notifications')
          .select('''
            *,
            related_user:users!related_user_id(*)
          ''')
          .eq('user_id', userId);

      if (unreadOnly) {
        query = query.eq('is_read', false);
      }

      query = query.order('created_at', ascending: false).limit(limit);

      final response = await query;

      return response.map<NotificationModel>((json) => NotificationModel.fromJson(json)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching notifications: $e');
      }
      return [];
    }
  }

  // Mark notification as read
  static Future<bool> markAsRead(String notificationId) async {
    try {
      await _client
          .from('notifications')
          .update({
            'is_read': true,
            'read_at': DateTime.now().toIso8601String(),
          })
          .eq('id', notificationId);

      if (kDebugMode) {
        print('✅ Notification marked as read: $notificationId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking notification as read: $e');
      }
      return false;
    }
  }

  // Mark all notifications as read for a user
  static Future<bool> markAllAsRead(String userId) async {
    try {
      await _client
          .from('notifications')
          .update({
            'is_read': true,
            'read_at': DateTime.now().toIso8601String(),
          })
          .eq('user_id', userId)
          .eq('is_read', false);

      if (kDebugMode) {
        print('✅ All notifications marked as read for user: $userId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error marking all notifications as read: $e');
      }
      return false;
    }
  }

  // Get unread notification count
  static Future<int> getUnreadCount(String userId) async {
    try {
      final response = await _client
          .from('notifications')
          .select('id')
          .eq('user_id', userId)
          .eq('is_read', false);

      return response.length;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error getting unread count: $e');
      }
      return 0;
    }
  }

  // Delete notification
  static Future<bool> deleteNotification(String notificationId) async {
    try {
      await _client
          .from('notifications')
          .delete()
          .eq('id', notificationId);

      if (kDebugMode) {
        print('✅ Notification deleted: $notificationId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error deleting notification: $e');
      }
      return false;
    }
  }

  // Subscribe to real-time notifications for a user
  static RealtimeChannel subscribeToUserNotifications(
    String userId,
    Function(NotificationModel) onNotificationReceived,
  ) {
    if (kDebugMode) {
      print('🔔 Subscribing to notifications for user: $userId');
    }

    final channel = _client
        .channel('notifications:$userId')
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: 'notifications',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'user_id',
            value: userId,
          ),
          callback: (payload) {
            try {
              final notification = NotificationModel.fromJson(payload.newRecord);
              onNotificationReceived(notification);
              
              if (kDebugMode) {
                print('🔔 New notification received: ${notification.title}');
              }
            } catch (e) {
              if (kDebugMode) {
                print('❌ Error processing notification: $e');
              }
            }
          },
        )
        .subscribe();

    return channel;
  }

  // Unsubscribe from notifications
  static Future<void> unsubscribeFromNotifications(RealtimeChannel channel) async {
    try {
      await _client.removeChannel(channel);
      if (kDebugMode) {
        print('✅ Unsubscribed from notifications');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error unsubscribing from notifications: $e');
      }
    }
  }

  // Send trip reminder notifications
  static Future<void> sendTripReminders() async {
    try {
      final now = DateTime.now();
      final tomorrow = now.add(const Duration(days: 1));
      final in30Minutes = now.add(const Duration(minutes: 30));

      // Get trips departing tomorrow (24h reminder)
      final tripsIn24h = await _client
          .from('trips')
          .select('''
            *,
            bookings!inner(passenger_id, status),
            leader:users!leader_id(*)
          ''')
          .eq('bookings.status', 'accepted')
          .gte('departure_date', tomorrow.toIso8601String().split('T')[0])
          .lt('departure_date', tomorrow.add(const Duration(days: 1)).toIso8601String().split('T')[0]);

      // Get trips departing in 30 minutes
      final tripsIn30Min = await _client
          .from('trips')
          .select('''
            *,
            bookings!inner(passenger_id, status),
            leader:users!leader_id(*)
          ''')
          .eq('bookings.status', 'accepted')
          .eq('departure_date', now.toIso8601String().split('T')[0])
          .gte('departure_time', in30Minutes.toIso8601String().split('T')[1].substring(0, 8))
          .lt('departure_time', in30Minutes.add(const Duration(minutes: 10)).toIso8601String().split('T')[1].substring(0, 8));

      // Send 24h reminders
      for (final trip in tripsIn24h) {
        final bookings = trip['bookings'] as List;
        for (final booking in bookings) {
          final notification = NotificationModel.createTripReminder(
            userId: booking['passenger_id'],
            tripId: trip['id'],
            tripDestination: trip['to_city'],
            departureTime: DateTime.parse('${trip['departure_date']} ${trip['departure_time']}'),
            reminderType: '24h',
          );
          await createNotification(notification);
        }
      }

      // Send 30min reminders
      for (final trip in tripsIn30Min) {
        final bookings = trip['bookings'] as List;
        for (final booking in bookings) {
          final notification = NotificationModel.createTripReminder(
            userId: booking['passenger_id'],
            tripId: trip['id'],
            tripDestination: trip['to_city'],
            departureTime: DateTime.parse('${trip['departure_date']} ${trip['departure_time']}'),
            reminderType: '30min',
          );
          await createNotification(notification);
        }
      }

      if (kDebugMode) {
        print('✅ Trip reminders sent successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending trip reminders: $e');
      }
    }
  }

  // Helper method to create and send notification in one call
  static Future<void> sendNotification({
    required String userId,
    required String title,
    required String body,
    String notificationType = 'general',
    String? tripId,
    String? bookingId,
    String? relatedUserId,
    Map<String, dynamic>? data,
  }) async {
    final notification = NotificationModel(
      id: '',
      userId: userId,
      title: title,
      body: body,
      notificationType: notificationType,
      tripId: tripId,
      bookingId: bookingId,
      relatedUserId: relatedUserId,
      data: data ?? {},
      createdAt: DateTime.now(),
    );

    await createNotification(notification);
  }

  // =====================================================
  // LOCAL NOTIFICATION METHODS
  // =====================================================

  /// Show a local notification
  static Future<void> showLocalNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
    Priority priority = Priority.defaultPriority,
  }) async {
    if (!_isInitialized) {
      await initialize();
    }

    const androidDetails = AndroidNotificationDetails(
      'safarni_channel',
      'سفرني',
      channelDescription: 'إشعارات تطبيق سفرني',
      importance: Importance.high,
      priority: Priority.high,
      showWhen: true,
      icon: '@mipmap/ic_launcher',
      color: Color(0xFF1565C0), // Primary color
    );

    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    const notificationDetails = NotificationDetails(
      android: androidDetails,
      iOS: iosDetails,
    );

    await _localNotifications.show(
      id,
      title,
      body,
      notificationDetails,
      payload: payload,
    );

    if (kDebugMode) {
      print('🔔 Local notification shown: $title');
    }
  }

  /// Show notification for new booking request
  static Future<void> showBookingRequestNotification({
    required String driverName,
    required String passengerName,
    required String tripRoute,
    required String bookingId,
  }) async {
    await showLocalNotification(
      id: bookingId.hashCode,
      title: 'طلب حجز جديد',
      body: 'طلب حجز من $passengerName للرحلة $tripRoute',
      payload: 'booking_request:$bookingId',
      priority: Priority.high,
    );
  }

  /// Show notification for booking acceptance
  static Future<void> showBookingAcceptedNotification({
    required String driverName,
    required String tripRoute,
    required String bookingId,
  }) async {
    await showLocalNotification(
      id: bookingId.hashCode,
      title: 'تم قبول حجزك',
      body: 'قبل $driverName حجزك للرحلة $tripRoute',
      payload: 'booking_accepted:$bookingId',
      priority: Priority.high,
    );
  }

  /// Show notification for booking rejection
  static Future<void> showBookingRejectedNotification({
    required String driverName,
    required String tripRoute,
    required String bookingId,
  }) async {
    await showLocalNotification(
      id: bookingId.hashCode,
      title: 'تم رفض حجزك',
      body: 'رفض $driverName حجزك للرحلة $tripRoute',
      payload: 'booking_rejected:$bookingId',
    );
  }

  /// Show notification for new message
  static Future<void> showNewMessageNotification({
    required String senderName,
    required String messageText,
    required String conversationId,
  }) async {
    await showLocalNotification(
      id: conversationId.hashCode,
      title: 'رسالة جديدة من $senderName',
      body: messageText.length > 50
          ? '${messageText.substring(0, 50)}...'
          : messageText,
      payload: 'message:$conversationId',
    );
  }

  /// Show trip reminder notification
  static Future<void> showTripReminderNotification({
    required String tripRoute,
    required DateTime departureTime,
    required String tripId,
    required String reminderType,
  }) async {
    String title;
    String body;

    switch (reminderType) {
      case '24h':
        title = 'تذكير: رحلتك غداً';
        body = 'رحلتك إلى $tripRoute غداً في ${_formatTime(departureTime)}';
        break;
      case '30min':
        title = 'تذكير: رحلتك قريباً';
        body = 'رحلتك إلى $tripRoute ستبدأ خلال 30 دقيقة';
        break;
      default:
        title = 'تذكير رحلة';
        body = 'رحلتك إلى $tripRoute';
    }

    await showLocalNotification(
      id: '$tripId-$reminderType'.hashCode,
      title: title,
      body: body,
      payload: 'trip_reminder:$tripId',
      priority: Priority.high,
    );
  }

  /// Cancel a specific notification
  static Future<void> cancelNotification(int id) async {
    await _localNotifications.cancel(id);
  }

  /// Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    await _localNotifications.cancelAll();
  }

  /// Get pending notifications
  static Future<List<PendingNotificationRequest>> getPendingNotifications() async {
    return await _localNotifications.pendingNotificationRequests();
  }

  // =====================================================
  // ENHANCED BOOKING NOTIFICATION METHODS
  // =====================================================

  /// Send comprehensive booking request notification
  static Future<void> sendBookingRequestNotification({
    required String driverId,
    required String passengerId,
    required String bookingId,
    required String tripRoute,
    required String passengerName,
  }) async {
    // Create database notification
    await sendNotification(
      userId: driverId,
      title: 'طلب حجز جديد',
      body: 'طلب حجز من $passengerName للرحلة $tripRoute',
      notificationType: 'booking_request',
      bookingId: bookingId,
      relatedUserId: passengerId,
    );

    // Show local notification
    await showBookingRequestNotification(
      driverName: '', // Driver name not needed for driver notification
      passengerName: passengerName,
      tripRoute: tripRoute,
      bookingId: bookingId,
    );
  }

  /// Send comprehensive booking acceptance notification
  static Future<void> sendBookingAcceptanceNotification({
    required String passengerId,
    required String driverId,
    required String bookingId,
    required String tripRoute,
    required String driverName,
  }) async {
    // Create database notification
    await sendNotification(
      userId: passengerId,
      title: 'تم قبول حجزك',
      body: 'قبل $driverName حجزك للرحلة $tripRoute',
      notificationType: 'booking_accepted',
      bookingId: bookingId,
      relatedUserId: driverId,
    );

    // Show local notification
    await showBookingAcceptedNotification(
      driverName: driverName,
      tripRoute: tripRoute,
      bookingId: bookingId,
    );
  }

  /// Send comprehensive booking rejection notification
  static Future<void> sendBookingRejectionNotification({
    required String passengerId,
    required String driverId,
    required String bookingId,
    required String tripRoute,
    required String driverName,
    String? rejectionReason,
  }) async {
    // Create database notification
    await sendNotification(
      userId: passengerId,
      title: 'تم رفض حجزك',
      body: rejectionReason != null
          ? 'رفض $driverName حجزك: $rejectionReason'
          : 'رفض $driverName حجزك للرحلة $tripRoute',
      notificationType: 'booking_rejected',
      bookingId: bookingId,
      relatedUserId: driverId,
    );

    // Show local notification
    await showBookingRejectedNotification(
      driverName: driverName,
      tripRoute: tripRoute,
      bookingId: bookingId,
    );
  }

  // =====================================================
  // UTILITY METHODS
  // =====================================================

  static String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
