import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/booking_model.dart';
import 'notification_service.dart';
import 'message_service.dart';

class BookingService {
  static final SupabaseClient _client = Supabase.instance.client;

  // Create a new booking request
  static Future<Map<String, dynamic>> createBooking({
    required String tripId,
    required String passengerId,
    required String driverId,
    required int seatsBooked,
    required double totalPrice,
    required String bookingType, // 'instant' or 'manual'
    String? message,
    String? specialRequests,
    Map<String, dynamic>? passengerDetails,
    String? passengerName,
    String? passengerPhone,
    bool hasLuggage = false,
  }) async {
    try {
      if (kDebugMode) {
        print('🎫 Creating booking: $tripId for passenger: $passengerId');
      }

      // First, check if trip has available seats
      final tripResponse = await _client
          .from('trips')
          .select('available_seats, total_seats, allow_instant_booking')
          .eq('id', tripId)
          .single();

      final availableSeats = tripResponse['available_seats'] as int;
      final allowInstantBooking =
          tripResponse['allow_instant_booking'] as bool? ?? false;

      if (availableSeats < seatsBooked) {
        return {
          'success': false,
          'error': 'عذراً، لا توجد مقاعد كافية متاحة',
        };
      }

      // Determine initial status based on booking type
      String initialStatus = 'pending';
      if (bookingType == 'instant' && allowInstantBooking) {
        initialStatus = 'accepted';
      }

      // Create the booking data with proper types for Supabase
      final Map<String, dynamic> bookingData = <String, dynamic>{
        // UUID fields (Strings)
        'trip_id': tripId,
        'passenger_id': passengerId,
        'driver_id': driverId,

        // Numeric fields (proper types)
        'seats_booked': seatsBooked, // int
        'total_price': totalPrice, // double

        // String fields
        'booking_type': bookingType, // 'فوري' or 'بالقبول'
        'status': initialStatus, // 'pending'
        'message': message, // String or null

        // IMPORTANT: special_requests is TEXT in database, not JSONB
        'special_requests': specialRequests, // String or null

        // IMPORTANT: passenger_details is JSONB - must be Map<String, dynamic>
        // ✅ CORRECT: Pass as raw Map, Supabase handles JSON conversion automatically
        'passenger_details': _buildPassengerDetailsMap(
          passengerDetails: passengerDetails,
          passengerName: passengerName,
          passengerPhone: passengerPhone,
          hasLuggage: hasLuggage,
        ), // This returns Map<String, dynamic>, NOT jsonEncode()
      };

      // ✅ CRITICAL: Validate data types before insertion
      _validateBookingDataTypes(bookingData);

      // Debug logging to verify data types
      if (kDebugMode) {
        print('🔍 Booking data types validation:');
        print('  trip_id: ${bookingData['trip_id'].runtimeType} ✅');
        print('  passenger_id: ${bookingData['passenger_id'].runtimeType} ✅');
        print('  driver_id: ${bookingData['driver_id'].runtimeType} ✅');
        print('  seats_booked: ${bookingData['seats_booked'].runtimeType} ✅');
        print('  total_price: ${bookingData['total_price'].runtimeType} ✅');
        print('  booking_type: ${bookingData['booking_type'].runtimeType} ✅');
        print('  status: ${bookingData['status'].runtimeType} ✅');
        print('  message: ${bookingData['message']?.runtimeType ?? 'null'} ✅');
        print(
            '  special_requests: ${bookingData['special_requests']?.runtimeType ?? 'null'} ✅');
        print(
            '  passenger_details: ${bookingData['passenger_details'].runtimeType} ✅');
        print('📋 All data types are correct for Supabase JSONB insertion');
      }

      // Insert booking into Supabase
      if (kDebugMode) {
        print('🚀 Inserting booking data to Supabase...');
        print('📋 Final data being sent: $bookingData');
      }

      final response =
          await _client.from('bookings').insert(bookingData).select().single();

      if (kDebugMode) {
        print('✅ Supabase insert successful');
        print('📋 Response from Supabase: $response');
        print(
            '🔍 Response passenger_details type: ${response['passenger_details']?.runtimeType ?? 'null'}');
        print(
            '🔍 Response passenger_details value: ${response['passenger_details']}');

        // ✅ CRITICAL: Show if passenger_details is a JSON string that needs decoding
        if (response['passenger_details'] is String) {
          print(
              '🔧 passenger_details is JSON string - will be decoded by BookingModel.fromJson');
        } else if (response['passenger_details'] is Map) {
          print('✅ passenger_details is already a Map - no decoding needed');
        }

        // Check updated_at field
        print(
            '🔍 Response updated_at type: ${response['updated_at']?.runtimeType ?? 'null'}');
        print('🔍 Response updated_at value: ${response['updated_at']}');
      }

      // ✅ CRITICAL: Safe parsing with detailed error handling
      late final BookingModel booking;
      try {
        booking = BookingModel.fromJson(response);
        if (kDebugMode) {
          print('✅ Booking created successfully: ${booking.id}');
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error parsing booking response: $e');
          print('❌ Response data: $response');
          print('❌ Response keys: ${response.keys.toList()}');
          response.forEach((key, value) {
            print('❌   $key: ${value?.runtimeType} = $value');
          });
        }
        throw Exception('Failed to parse booking response: $e');
      }

      // If instant booking, update available seats immediately
      if (initialStatus == 'accepted') {
        await _client.from('trips').update(
            {'available_seats': availableSeats - seatsBooked}).eq('id', tripId);

        if (kDebugMode) {
          print('✅ Trip seats updated for instant booking');
        }
      }

      // Send notification to driver (unless it's instant booking)
      if (initialStatus == 'pending') {
        await _sendBookingRequestNotification(booking);
      } else {
        // For instant bookings, send acceptance notification to passenger
        await _sendBookingAcceptedNotification(booking);
      }

      return {
        'success': true,
        'booking': booking,
        'message': initialStatus == 'accepted'
            ? 'تم تأكيد حجزك فوراً!'
            : 'تم إرسال طلب الحجز بنجاح',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error creating booking: $e');
      }
      return {
        'success': false,
        'error': 'حدث خطأ أثناء إنشاء الحجز: $e',
      };
    }
  }

  // Accept a booking request with conversation creation
  static Future<Map<String, dynamic>> acceptBooking(String bookingId) async {
    try {
      if (kDebugMode) {
        print('✅ Accepting booking: $bookingId');
      }

      // Get booking details first
      final bookingResponse = await _client.from('bookings').select('''
            *,
            trip:trips(*),
            passenger:users!passenger_id(*),
            driver:users!driver_id(*)
          ''').eq('id', bookingId).single();

      final booking = BookingModel.fromJson(bookingResponse);
      final trip = booking.trip!;

      // Check if seats are still available
      if (trip.availableSeats < booking.seatsBooked) {
        return {
          'success': false,
          'error': 'عذراً، لا توجد مقاعد كافية متاحة',
        };
      }

      // Update booking status
      await _client.from('bookings').update({
        'status': 'accepted',
        'confirmed_at': DateTime.now().toIso8601String(),
      }).eq('id', bookingId);

      // Update trip available seats (handled by trigger, but we can do it here too for immediate feedback)
      await _client.from('trips').update({
        'available_seats': trip.availableSeats - booking.seatsBooked
      }).eq('id', trip.id);

      // Send notification to passenger
      await _sendBookingAcceptedNotification(booking);

      // Create conversation using RPC function
      try {
        final conversationResult =
            await _client.rpc('get_or_create_conversation', params: {
          'p_booking_id': booking.id,
          'p_driver_id': booking.driverId,
          'p_passenger_id': booking.passengerId,
          'p_trip_id': booking.tripId,
        }).single();

        final conversationId = conversationResult['conversation_id'];

        if (kDebugMode) {
          print('✅ Conversation created/found: $conversationId');
        }

        // Send system welcome message
        await _client.from('messages').insert({
          'trip_id': booking.tripId,
          'booking_id': booking.id,
          'sender_id': booking.driverId,
          'receiver_id': booking.passengerId,
          'content':
              'مرحباً! تم قبول طلب حجزك. يمكنك التواصل معي هنا لأي استفسارات.',
          'message_type': 'system',
          'is_read': false,
          'is_delivered': true,
        });

        // Update conversation with last message
        await _client.from('conversations').update({
          'last_message':
              'مرحباً! تم قبول طلب حجزك. يمكنك التواصل معي هنا لأي استفسارات.',
          'unread_count_passenger': 1,
          'updated_at': DateTime.now().toIso8601String(),
        }).eq('id', conversationId);

        if (kDebugMode) {
          print('✅ Conversation and system message created successfully');
        }
      } catch (conversationError) {
        if (kDebugMode) {
          print(
              '⚠️ Warning: Could not create conversation: $conversationError');
        }
        // Continue with booking acceptance even if conversation creation fails
      }

      if (kDebugMode) {
        print('✅ Booking accepted successfully');
      }

      return {
        'success': true,
        'message': 'تم قبول الحجز بنجاح',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error accepting booking: $e');
      }
      return {
        'success': false,
        'error': 'حدث خطأ أثناء قبول الحجز: $e',
      };
    }
  }

  // Reject a booking request
  static Future<Map<String, dynamic>> rejectBooking(
    String bookingId, {
    String? rejectionReason,
  }) async {
    try {
      if (kDebugMode) {
        print('❌ Rejecting booking: $bookingId');
      }

      // Get booking details first
      final bookingResponse = await _client.from('bookings').select('''
            *,
            trip:trips(*),
            passenger:users!passenger_id(*)
          ''').eq('id', bookingId).single();

      final booking = BookingModel.fromJson(bookingResponse);

      // Update booking status
      await _client.from('bookings').update({
        'status': 'rejected',
        'rejection_reason': rejectionReason,
      }).eq('id', bookingId);

      // Send notification to passenger
      await _sendBookingRejectedNotification(booking, rejectionReason);

      if (kDebugMode) {
        print('✅ Booking rejected successfully');
      }

      return {
        'success': true,
        'message': 'تم رفض الحجز',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error rejecting booking: $e');
      }
      return {
        'success': false,
        'error': 'حدث خطأ أثناء رفض الحجز: $e',
      };
    }
  }

  // Get bookings for a user (as passenger) using database function
  static Future<List<BookingModel>> getPassengerBookings(String userId) async {
    try {
      if (kDebugMode) {
        print('🎫 Fetching all passenger bookings for user: $userId');
      }

      // Use database function for better performance and consistency
      final response = await _client.rpc('get_passenger_bookings', params: {
        'p_passenger_id': userId,
        'p_status': null, // Get all statuses
      });

      if (response == null) return [];

      return (response as List).map<BookingModel>((json) {
        // Transform the function result to match BookingModel.fromJson expectations
        final bookingData = {
          'id': json['id'],
          'trip_id': json['trip_id'],
          'passenger_id': json['passenger_id'],
          'driver_id': json['driver_id'],
          'seats_booked': json['seats_booked'],
          'total_price': json['total_price'],
          'status': json['status'],
          'booking_type': json['booking_type'],
          'message': json['message'],
          'special_requests': json['special_requests'],
          'passenger_details': json['passenger_details'],
          'has_luggage': json['has_luggage'],
          'created_at': json['created_at'],
          'confirmed_at': json['confirmed_at'],
          'trip': json['trip_data'],
          'driver': json['driver_data'],
        };
        return BookingModel.fromJson(bookingData);
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching passenger bookings: $e');
      }

      // Fallback to direct query if function fails
      try {
        final response = await _client
            .from('bookings')
            .select('''
              *,
              trip:trips(*),
              driver:users!driver_id(*)
            ''')
            .eq('passenger_id', userId)
            .order('created_at', ascending: false);

        return response
            .map<BookingModel>((json) => BookingModel.fromJson(json))
            .toList();
      } catch (fallbackError) {
        if (kDebugMode) {
          print('❌ Fallback query also failed: $fallbackError');
        }
        return [];
      }
    }
  }

  // Get comprehensive driver bookings with all required fields and joins
  static Future<List<BookingModel>> getDriverBookings(String driverId) async {
    try {
      if (kDebugMode) {
        print('🚗 Fetching comprehensive driver bookings for: $driverId');
      }

      // Use comprehensive query with all required joins and fields
      final response = await _client.from('bookings').select('''
            id,
            trip_id,
            passenger_id,
            driver_id,
            seats_booked,
            total_price,
            booking_type,
            status,
            message,
            special_requests,
            passenger_details,
            is_paid,
            payment_method,
            payment_reference,
            confirmed_at,
            cancelled_at,
            completed_at,
            created_at,
            updated_at,
            trip:trips(
              id,
              title,
              description,
              from_city,
              to_city,
              departure_date,
              departure_time,
              return_date,
              return_time,
              price,
              total_seats,
              available_seats,
              trip_type,
              status,
              car_type,
              car_color,
              car_plate,
              duration_minutes,
              amenities,
              meeting_point,
              notes,
              rules
            ),
            passenger:users!passenger_id(
              id,
              full_name,
              phone,
              profile_image_url,
              rating,
              total_ratings,
              total_trips,
              city,
              created_at
            )
          ''').eq('driver_id', driverId).order('created_at', ascending: false);

      if (kDebugMode) {
        print('✅ Retrieved ${response.length} comprehensive driver bookings');
        if (response.isNotEmpty) {
          final statusCounts = <String, int>{};
          for (final booking in response) {
            final status = booking['status'] as String;
            statusCounts[status] = (statusCounts[status] ?? 0) + 1;
          }
          print('   Status breakdown: $statusCounts');
        }
      }

      return response.map<BookingModel>((json) {
        try {
          return BookingModel.fromJson(json);
        } catch (e) {
          if (kDebugMode) {
            print('⚠️ Error parsing booking ${json['id']}: $e');
            print('   Raw data: $json');
          }
          // Return a minimal booking model to avoid breaking the list
          return BookingModel(
            id: json['id'] ?? '',
            tripId: json['trip_id'] ?? '',
            passengerId: json['passenger_id'] ?? '',
            driverId: json['driver_id'] ?? '',
            seatsBooked: json['seats_booked'] ?? 1,
            totalPrice: (json['total_price'] ?? 0).toDouble(),
            status: json['status'] ?? 'pending',
            bookingType: json['booking_type'] ?? 'manual',
            createdAt:
                DateTime.tryParse(json['created_at'] ?? '') ?? DateTime.now(),
            updatedAt:
                DateTime.tryParse(json['updated_at'] ?? '') ?? DateTime.now(),
          );
        }
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching comprehensive driver bookings: $e');
      }
      return [];
    }
  }

  // Get real-time comprehensive booking stream for driver
  static Stream<List<BookingModel>> getDriverBookingsStream(String driverId) {
    if (kDebugMode) {
      print(
          '📡 Setting up comprehensive real-time booking stream for driver: $driverId');
    }

    return _client
        .from('bookings')
        .stream(primaryKey: ['id'])
        .eq('driver_id', driverId)
        .order('created_at', ascending: false)
        .asyncMap((data) async {
          if (kDebugMode) {
            print(
                '📡 Received ${data.length} bookings in stream, fetching full details...');
          }

          // For real-time updates, we need to fetch full details including joins
          // since Supabase streams don't support complex joins
          final List<BookingModel> fullBookings = [];

          for (final bookingData in data) {
            try {
              // Fetch full booking details with joins
              final fullBooking = await _client.from('bookings').select('''
                    id,
                    trip_id,
                    passenger_id,
                    driver_id,
                    seats_booked,
                    total_price,
                    booking_type,
                    status,
                    message,
                    special_requests,
                    passenger_details,
                    is_paid,
                    payment_method,
                    payment_reference,
                    confirmed_at,
                    cancelled_at,
                    completed_at,
                    created_at,
                    updated_at,
                    trip:trips(
                      id,
                      title,
                      description,
                      from_city,
                      to_city,
                      departure_date,
                      departure_time,
                      return_date,
                      return_time,
                      price,
                      total_seats,
                      available_seats,
                      trip_type,
                      status,
                      car_type,
                      car_color,
                      car_plate,
                      duration_minutes,
                      amenities,
                      meeting_point,
                      notes,
                      rules
                    ),
                    passenger:users!passenger_id(
                      id,
                      full_name,
                      phone,
                      profile_image_url,
                      rating,
                      total_ratings,
                      total_trips,
                      city,
                      created_at
                    )
                  ''').eq('id', bookingData['id']).single();

              fullBookings.add(BookingModel.fromJson(fullBooking));
            } catch (e) {
              if (kDebugMode) {
                print(
                    '⚠️ Error fetching full booking details for ${bookingData['id']}: $e');
              }
              // Add minimal booking as fallback
              fullBookings.add(BookingModel(
                id: bookingData['id'] ?? '',
                tripId: bookingData['trip_id'] ?? '',
                passengerId: bookingData['passenger_id'] ?? '',
                driverId: bookingData['driver_id'] ?? '',
                seatsBooked: bookingData['seats_booked'] ?? 1,
                totalPrice: (bookingData['total_price'] ?? 0).toDouble(),
                status: bookingData['status'] ?? 'pending',
                bookingType: bookingData['booking_type'] ?? 'manual',
                createdAt: DateTime.tryParse(bookingData['created_at'] ?? '') ??
                    DateTime.now(),
                updatedAt: DateTime.tryParse(bookingData['updated_at'] ?? '') ??
                    DateTime.now(),
              ));
            }
          }

          if (kDebugMode) {
            print('📡 Stream processed ${fullBookings.length} full bookings');
          }

          return fullBookings;
        });
  }

  // Get pending booking requests for a driver
  static Future<List<BookingModel>> getPendingBookings(String driverId) async {
    try {
      if (kDebugMode) {
        print('🚗 Fetching pending bookings for driver: $driverId');
      }

      // First try using the database function with enhanced debugging
      try {
        if (kDebugMode) {
          print('🔍 Attempting to call get_driver_bookings function...');
          print('   Parameters: driver_id=$driverId, status=pending');
        }

        final response = await _client.rpc('get_driver_bookings', params: {
          'p_driver_id': driverId,
          'p_status': 'pending',
        });

        if (kDebugMode) {
          print('📊 Function response type: ${response.runtimeType}');
          print('📊 Function response: $response');
        }

        if (response != null && response is List) {
          if (kDebugMode) {
            print(
                '✅ Retrieved ${response.length} pending bookings via function');
            if (response.isNotEmpty) {
              print('   Sample booking keys: ${response.first.keys.toList()}');
            }
          }

          return response.map<BookingModel>((bookingData) {
            try {
              // Convert the function result to proper BookingModel format
              final Map<String, dynamic> json = {
                'id': bookingData['id'],
                'trip_id': bookingData['trip_id'],
                'passenger_id': bookingData['passenger_id'],
                'driver_id': bookingData['driver_id'],
                'seats_booked': bookingData['seats_booked'],
                'total_price': bookingData['total_price'],
                'status': bookingData['status'],
                'booking_type': bookingData['booking_type'] ?? 'manual',
                'message': bookingData['message'],
                'special_requests': bookingData['special_requests'],
                'passenger_details': bookingData['passenger_details'] ?? {},
                'has_luggage': bookingData['has_luggage'] ?? false,
                'created_at': bookingData['created_at'],
                'confirmed_at': bookingData['confirmed_at'],
                'updated_at': bookingData['created_at'], // Fallback
                'trip': bookingData['trip_data'],
                'passenger': bookingData['passenger_data'],
              };

              if (kDebugMode) {
                print('   Converting booking: ${json['id']}');
              }

              return BookingModel.fromJson(json);
            } catch (conversionError) {
              if (kDebugMode) {
                print('❌ Error converting booking data: $conversionError');
                print('   Raw booking data: $bookingData');
              }
              rethrow;
            }
          }).toList();
        } else {
          if (kDebugMode) {
            print('⚠️ Function returned null or non-list response');
          }
        }
      } catch (functionError) {
        if (kDebugMode) {
          print('⚠️ Database function failed: $functionError');
          print('   Error type: ${functionError.runtimeType}');
          print('   Using fallback direct query...');
        }
      }

      // Fallback to direct query with enhanced debugging
      if (kDebugMode) {
        print('🔄 Executing fallback direct query...');
        print(
            '   Query: bookings where driver_id=$driverId AND status=pending');
      }

      final response = await _client
          .from('bookings')
          .select('''
            *,
            trip:trips(*),
            passenger:users!passenger_id(*)
          ''')
          .eq('driver_id', driverId)
          .eq('status', 'pending')
          .order('created_at', ascending: false);

      if (kDebugMode) {
        print(
            '✅ Retrieved ${response.length} pending bookings via direct query');
        if (response.isNotEmpty) {
          final sample = response.first;
          print('   Sample booking:');
          print('     - ID: ${sample['id']}');
          print('     - Status: ${sample['status']}');
          print('     - Driver ID: ${sample['driver_id']}');
          print('     - Passenger ID: ${sample['passenger_id']}');
          print(
              '     - Trip data: ${sample['trip'] != null ? 'Present' : 'Missing'}');
          print(
              '     - Passenger data: ${sample['passenger'] != null ? 'Present' : 'Missing'}');
          print('     - Created: ${sample['created_at']}');
        } else {
          print('   ⚠️ No pending bookings found for driver $driverId');

          // Additional debugging: check if there are ANY bookings for this driver
          final allDriverBookings = await _client
              .from('bookings')
              .select('id, status, driver_id, created_at')
              .eq('driver_id', driverId);

          print(
              '   📊 Total bookings for this driver: ${allDriverBookings.length}');
          if (allDriverBookings.isNotEmpty) {
            final statusCounts = <String, int>{};
            for (final booking in allDriverBookings) {
              final status = booking['status'] as String;
              statusCounts[status] = (statusCounts[status] ?? 0) + 1;
            }
            print('   📊 Status breakdown: $statusCounts');
          }
        }
      }

      return response
          .map<BookingModel>((json) => BookingModel.fromJson(json))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching pending bookings: $e');
      }
      return [];
    }
  }

  // Get booking by ID
  static Future<BookingModel?> getBookingById(String bookingId) async {
    try {
      final response = await _client.from('bookings').select('''
            *,
            trip:trips(*),
            passenger:users!passenger_id(*),
            driver:users!driver_id(*)
          ''').eq('id', bookingId).single();

      return BookingModel.fromJson(response);
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching booking: $e');
      }
      return null;
    }
  }

  // Private helper methods for notifications
  static Future<void> _sendBookingRequestNotification(
      BookingModel booking) async {
    try {
      // Use the enhanced notification service
      await NotificationService.sendBookingRequestNotification(
        driverId: booking.driverId,
        passengerId: booking.passengerId,
        bookingId: booking.id,
        tripRoute:
            '${booking.trip?.fromCity ?? 'غير محدد'} ← ${booking.trip?.toCity ?? 'غير محدد'}',
        passengerName: booking.passenger?.fullName ?? 'مسافر',
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending booking request notification: $e');
      }
    }
  }

  static Future<void> _sendBookingAcceptedNotification(
      BookingModel booking) async {
    try {
      // Use the enhanced notification service
      await NotificationService.sendBookingAcceptanceNotification(
        passengerId: booking.passengerId,
        driverId: booking.driverId,
        bookingId: booking.id,
        tripRoute:
            '${booking.trip?.fromCity ?? 'غير محدد'} ← ${booking.trip?.toCity ?? 'غير محدد'}',
        driverName: booking.driver?.fullName ?? 'السائق',
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending booking accepted notification: $e');
      }
    }
  }

  static Future<void> _sendBookingRejectedNotification(
    BookingModel booking,
    String? rejectionReason,
  ) async {
    try {
      // Use the enhanced notification service
      await NotificationService.sendBookingRejectionNotification(
        passengerId: booking.passengerId,
        driverId: booking.driverId,
        bookingId: booking.id,
        tripRoute:
            '${booking.trip?.fromCity ?? 'غير محدد'} ← ${booking.trip?.toCity ?? 'غير محدد'}',
        driverName: booking.driver?.fullName ?? 'السائق',
        rejectionReason: rejectionReason,
      );
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error sending booking rejected notification: $e');
      }
    }
  }

  // Helper method to build passenger details as proper Map<String, dynamic>
  static Map<String, dynamic> _buildPassengerDetailsMap({
    Map<String, dynamic>? passengerDetails,
    String? passengerName,
    String? passengerPhone,
    bool hasLuggage = false,
  }) {
    // If custom passenger details are provided, use them
    if (passengerDetails != null && passengerDetails.isNotEmpty) {
      return Map<String, dynamic>.from(passengerDetails);
    }

    // Otherwise, build from individual parameters
    return <String, dynamic>{
      'name': passengerName ?? '',
      'phone': passengerPhone ?? '',
      'has_luggage': hasLuggage,
      'created_at': DateTime.now().toIso8601String(),
    };
  }

  // ✅ CRITICAL: Validate booking data types before Supabase insertion
  static void _validateBookingDataTypes(Map<String, dynamic> bookingData) {
    // Validate required string fields
    if (bookingData['trip_id'] is! String) {
      throw ArgumentError(
          'trip_id must be String, got ${bookingData['trip_id'].runtimeType}');
    }
    if (bookingData['passenger_id'] is! String) {
      throw ArgumentError(
          'passenger_id must be String, got ${bookingData['passenger_id'].runtimeType}');
    }
    if (bookingData['driver_id'] is! String) {
      throw ArgumentError(
          'driver_id must be String, got ${bookingData['driver_id'].runtimeType}');
    }

    // Validate numeric fields
    if (bookingData['seats_booked'] is! int) {
      throw ArgumentError(
          'seats_booked must be int, got ${bookingData['seats_booked'].runtimeType}');
    }
    if (bookingData['total_price'] is! num) {
      throw ArgumentError(
          'total_price must be num, got ${bookingData['total_price'].runtimeType}');
    }

    // Validate JSONB field (most critical)
    if (bookingData['passenger_details'] is! Map<String, dynamic>) {
      throw ArgumentError(
          'passenger_details must be Map<String, dynamic>, got ${bookingData['passenger_details'].runtimeType}');
    }

    // Validate optional string fields (can be null)
    final message = bookingData['message'];
    if (message != null && message is! String) {
      throw ArgumentError(
          'message must be String or null, got ${message.runtimeType}');
    }

    final specialRequests = bookingData['special_requests'];
    if (specialRequests != null && specialRequests is! String) {
      throw ArgumentError(
          'special_requests must be String or null, got ${specialRequests.runtimeType}');
    }

    if (kDebugMode) {
      print('✅ All booking data types validated successfully');
    }
  }

  // =====================================================
  // SAMPLE DATA CREATION FOR TESTING
  // =====================================================

  /// Create sample booking data for testing (only in debug mode)
  static Future<Map<String, dynamic>> createSampleBookingData({
    required String driverId,
    required String tripId,
  }) async {
    if (!kDebugMode) {
      return {
        'success': false,
        'error': 'Sample data creation only available in debug mode',
      };
    }

    try {
      print('🧪 Creating sample booking data for driver: $driverId');

      // Create a sample passenger user if not exists
      final samplePassengerId =
          'sample-passenger-${DateTime.now().millisecondsSinceEpoch}';

      await _client.from('users').upsert({
        'id': samplePassengerId,
        'email': '<EMAIL>',
        'full_name': 'فاطمة الزهراء',
        'phone': '+212612345678',
        'role': 'passenger',
        'city': 'الرباط',
        'profile_image_url': null,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      // Create sample booking
      final sampleBookingId =
          'sample-booking-${DateTime.now().millisecondsSinceEpoch}';

      await _client.from('bookings').insert({
        'id': sampleBookingId,
        'trip_id': tripId,
        'passenger_id': samplePassengerId,
        'driver_id': driverId,
        'seats_booked': 1,
        'total_price': 120.0,
        'booking_type': 'manual',
        'status': 'pending',
        'message': 'أتطلع للرحلة معكم، أرجو قبول طلب الحجز',
        'special_requests': 'مقعد بجانب النافذة إذا أمكن',
        'passenger_details': {
          'passengers': [
            {
              'name': 'فاطمة الزهراء',
              'phone': '+212612345678',
              'age': 28,
            }
          ]
        },
        'is_paid': false,
        'created_at': DateTime.now().toIso8601String(),
        'updated_at': DateTime.now().toIso8601String(),
      });

      print('✅ Sample booking created: $sampleBookingId');

      return {
        'success': true,
        'message': 'تم إنشاء بيانات تجريبية بنجاح',
        'booking_id': sampleBookingId,
        'passenger_id': samplePassengerId,
      };
    } catch (e) {
      print('❌ Error creating sample booking data: $e');
      return {
        'success': false,
        'error': 'فشل في إنشاء البيانات التجريبية: $e',
      };
    }
  }

  // =====================================================
  // REAL-TIME SUBSCRIPTION METHODS
  // =====================================================

  /// Subscribe to booking changes for a specific user (as passenger)
  static RealtimeChannel subscribeToPassengerBookings({
    required String passengerId,
    required Function(BookingModel) onBookingUpdate,
    Function(String)? onError,
  }) {
    if (kDebugMode) {
      print('🔔 Subscribing to passenger bookings for: $passengerId');
    }

    final channel = _client
        .channel('passenger_bookings:$passengerId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'bookings',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'passenger_id',
            value: passengerId,
          ),
          callback: (payload) async {
            try {
              if (kDebugMode) {
                print('📨 Passenger booking update: ${payload.eventType}');
              }

              // Fetch the complete booking data with relations
              final bookingId = payload.newRecord['id'] as String;
              final booking = await getBookingById(bookingId);

              if (booking != null) {
                onBookingUpdate(booking);
              }
            } catch (e) {
              if (kDebugMode) {
                print('❌ Error processing passenger booking update: $e');
              }
              onError?.call(e.toString());
            }
          },
        )
        .subscribe();

    return channel;
  }

  /// Subscribe to booking requests for a specific driver
  static RealtimeChannel subscribeToDriverBookings({
    required String driverId,
    required Function(BookingModel) onBookingUpdate,
    Function(String)? onError,
  }) {
    if (kDebugMode) {
      print('🔔 Subscribing to driver bookings for: $driverId');
    }

    final channel = _client
        .channel('driver_bookings:$driverId')
        .onPostgresChanges(
          event: PostgresChangeEvent.all,
          schema: 'public',
          table: 'bookings',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'driver_id',
            value: driverId,
          ),
          callback: (payload) async {
            try {
              if (kDebugMode) {
                print('📨 Driver booking update: ${payload.eventType}');
              }

              // Fetch the complete booking data with relations
              final bookingId = payload.newRecord['id'] as String;
              final booking = await getBookingById(bookingId);

              if (booking != null) {
                onBookingUpdate(booking);
              }
            } catch (e) {
              if (kDebugMode) {
                print('❌ Error processing driver booking update: $e');
              }
              onError?.call(e.toString());
            }
          },
        )
        .subscribe();

    return channel;
  }

  /// Subscribe to pending booking requests for a driver (for requests screen)
  static RealtimeChannel subscribeToPendingBookings({
    required String driverId,
    required Function(BookingModel) onNewBookingRequest,
    required Function(String) onBookingStatusChange,
    Function(String)? onError,
  }) {
    if (kDebugMode) {
      print('🔔 Subscribing to pending bookings for driver: $driverId');
    }

    final channel = _client
        .channel('pending_bookings:$driverId')
        .onPostgresChanges(
          event: PostgresChangeEvent.insert,
          schema: 'public',
          table: 'bookings',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'driver_id',
            value: driverId,
          ),
          callback: (payload) async {
            try {
              final newRecord = payload.newRecord;
              if (newRecord['status'] == 'pending') {
                if (kDebugMode) {
                  print('📨 New booking request received');
                }

                // Fetch the complete booking data with relations
                final bookingId = newRecord['id'] as String;
                final booking = await getBookingById(bookingId);

                if (booking != null) {
                  onNewBookingRequest(booking);
                }
              }
            } catch (e) {
              if (kDebugMode) {
                print('❌ Error processing new booking request: $e');
              }
              onError?.call(e.toString());
            }
          },
        )
        .onPostgresChanges(
          event: PostgresChangeEvent.update,
          schema: 'public',
          table: 'bookings',
          filter: PostgresChangeFilter(
            type: PostgresChangeFilterType.eq,
            column: 'driver_id',
            value: driverId,
          ),
          callback: (payload) async {
            try {
              final oldRecord = payload.oldRecord;
              final newRecord = payload.newRecord;

              // Only notify if status changed from pending
              if (oldRecord['status'] == 'pending' &&
                  newRecord['status'] != 'pending') {
                if (kDebugMode) {
                  print(
                      '📨 Booking status changed from pending to ${newRecord['status']}');
                }

                onBookingStatusChange(newRecord['id'] as String);
              }
            } catch (e) {
              if (kDebugMode) {
                print('❌ Error processing booking status change: $e');
              }
              onError?.call(e.toString());
            }
          },
        )
        .subscribe();

    return channel;
  }

  /// Unsubscribe from booking updates
  static void unsubscribeFromBookings(RealtimeChannel channel) {
    try {
      _client.removeChannel(channel);
      if (kDebugMode) {
        print('🔕 Unsubscribed from booking updates');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error unsubscribing from bookings: $e');
      }
    }
  }

  // =====================================================
  // DIRECT QUERY METHODS FOR RELIABLE DRIVER BOOKINGS
  // =====================================================

  /// Get driver bookings using direct SELECT (most reliable)
  static Future<List<Map<String, dynamic>>> getDriverBookingsRaw({
    required String driverId,
    String? status,
    int limit = 100,
    int offset = 0,
  }) async {
    try {
      if (kDebugMode) {
        print('🚗 Fetching driver bookings for: $driverId (status: $status)');
      }

      // Build the query step by step
      dynamic queryBuilder = _client.from('bookings').select('''
            id, trip_id, passenger_id, driver_id, seats_booked, created_at,
            booking_type, message, passenger_details, special_requests,
            status, total_price, is_paid, payment_method, payment_reference,
            confirmed_at, cancelled_at, completed_at, updated_at,
            trip:trips(
              id, title, description, from_city, to_city, departure_date,
              departure_time, return_date, return_time, price, total_seats,
              available_seats, trip_type, status, car_type, car_color,
              car_plate, duration_minutes, amenities, meeting_point, notes, rules
            ),
            passenger:users!passenger_id(
              id, full_name, phone, profile_image_url, rating,
              total_ratings, total_trips, city, created_at
            )
          ''').eq('driver_id', driverId);

      if (status != null) {
        queryBuilder = queryBuilder.eq('status', status);
      }

      queryBuilder = queryBuilder.order('created_at', ascending: false);

      final response = await queryBuilder.range(offset, offset + limit - 1);

      if (kDebugMode) {
        print('✅ Raw query returned ${response.length} bookings');
        if (response.isNotEmpty) {
          print('   Sample booking: ${response.first['id']}');
          print('   Status: ${response.first['status']}');
          print('   Driver ID: ${response.first['driver_id']}');
        }
      }

      return response.map((r) => Map<String, dynamic>.from(r)).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error in getDriverBookingsRaw: $e');
      }
      throw Exception('Supabase booking fetch error: $e');
    }
  }

  /// Parse passenger details safely (handles both String and Map types)
  static Map<String, dynamic>? parsePassengerDetails(dynamic raw) {
    if (raw == null) return null;

    if (raw is Map) {
      return Map<String, dynamic>.from(raw);
    }

    if (raw is String && raw.isNotEmpty) {
      try {
        final decoded = jsonDecode(raw);
        if (decoded is Map) {
          return Map<String, dynamic>.from(decoded);
        }
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error parsing passenger_details JSON: $e');
        }
      }
    }

    return null;
  }

  // =====================================================
  // ENHANCED BOOKING MANAGEMENT METHODS
  // =====================================================

  /// Get bookings by status for a passenger using database function
  static Future<List<BookingModel>> getPassengerBookingsByStatus({
    required String passengerId,
    required String status,
  }) async {
    try {
      if (kDebugMode) {
        print('🎫 Fetching passenger bookings: $passengerId, status: $status');
      }

      // Use database function for better performance and consistency
      final response = await _client.rpc('get_passenger_bookings', params: {
        'p_passenger_id': passengerId,
        'p_status': status,
      });

      if (response == null) return [];

      return (response as List).map<BookingModel>((json) {
        // Transform the function result to match BookingModel.fromJson expectations
        final bookingData = {
          'id': json['id'],
          'trip_id': json['trip_id'],
          'passenger_id': json['passenger_id'],
          'driver_id': json['driver_id'],
          'seats_booked': json['seats_booked'],
          'total_price': json['total_price'],
          'status': json['status'],
          'booking_type': json['booking_type'],
          'message': json['message'],
          'special_requests': json['special_requests'],
          'passenger_details': json['passenger_details'],
          'has_luggage': json['has_luggage'],
          'created_at': json['created_at'],
          'confirmed_at': json['confirmed_at'],
          'trip': json['trip_data'],
          'driver': json['driver_data'],
        };
        return BookingModel.fromJson(bookingData);
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching passenger bookings by status: $e');
      }

      // Fallback to direct query if function fails
      try {
        final response = await _client
            .from('bookings')
            .select('''
              *,
              trip:trips(*),
              driver:users!driver_id(*)
            ''')
            .eq('passenger_id', passengerId)
            .eq('status', status)
            .order('created_at', ascending: false);

        return response
            .map<BookingModel>((json) => BookingModel.fromJson(json))
            .toList();
      } catch (fallbackError) {
        if (kDebugMode) {
          print('❌ Fallback query also failed: $fallbackError');
        }
        return [];
      }
    }
  }

  /// Get bookings by status for a driver using direct query (more reliable)
  static Future<List<BookingModel>> getDriverBookingsByStatus({
    required String driverId,
    required String status,
  }) async {
    try {
      if (kDebugMode) {
        print('🚗 Fetching driver bookings: $driverId, status: $status');
      }

      // First, check if there are ANY bookings for this driver
      final allBookingsCount = await _client
          .from('bookings')
          .select('id')
          .eq('driver_id', driverId)
          .count();

      if (kDebugMode) {
        print('📊 Total bookings for driver: $allBookingsCount');
      }

      // Use direct query for better reliability and debugging
      final response = await _client
          .from('bookings')
          .select('''
            id,
            trip_id,
            passenger_id,
            driver_id,
            seats_booked,
            total_price,
            booking_type,
            status,
            message,
            special_requests,
            passenger_details,
            is_paid,
            payment_method,
            payment_reference,
            confirmed_at,
            cancelled_at,
            completed_at,
            created_at,
            updated_at,
            trip:trips(
              id,
              title,
              description,
              from_city,
              to_city,
              departure_date,
              departure_time,
              return_date,
              return_time,
              price,
              total_seats,
              available_seats,
              trip_type,
              status,
              car_type,
              car_color,
              car_plate,
              duration_minutes,
              amenities,
              meeting_point,
              notes,
              rules,
              leader_id
            ),
            passenger:users!passenger_id(
              id,
              full_name,
              phone,
              profile_image_url,
              rating,
              total_ratings,
              total_trips,
              city,
              created_at
            )
          ''')
          .eq('driver_id', driverId)
          .eq('status', status)
          .order('created_at', ascending: false);

      if (kDebugMode) {
        print(
            '✅ Direct query returned ${response.length} bookings for status: $status');
        if (response.isNotEmpty) {
          print('   Sample booking: ${response.first['id']}');
          print(
              '   Trip data: ${response.first['trip'] != null ? 'Present' : 'Missing'}');
          print(
              '   Passenger data: ${response.first['passenger'] != null ? 'Present' : 'Missing'}');
        }
      }

      return response
          .map<BookingModel>((json) => BookingModel.fromJson(json))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error fetching driver bookings by status: $e');
      }

      // Try the database function as fallback
      try {
        if (kDebugMode) {
          print('🔄 Trying database function as fallback...');
        }

        final response = await _client.rpc('get_driver_bookings', params: {
          'p_driver_id': driverId,
          'p_status': status,
        });

        if (response == null) return [];

        if (kDebugMode) {
          print('📊 Function returned ${response.length} bookings');
        }

        return (response as List).map<BookingModel>((json) {
          // Transform the function result to match BookingModel.fromJson expectations
          final bookingData = {
            'id': json['id'],
            'trip_id': json['trip_id'],
            'passenger_id': json['passenger_id'],
            'driver_id': json['driver_id'],
            'seats_booked': json['seats_booked'],
            'total_price': json['total_price'],
            'status': json['status'],
            'booking_type': json['booking_type'],
            'message': json['message'],
            'special_requests': json['special_requests'],
            'passenger_details': json['passenger_details'],
            'has_luggage': json['has_luggage'],
            'created_at': json['created_at'],
            'confirmed_at': json['confirmed_at'],
            'trip': json['trip_data'],
            'passenger': json['passenger_data'],
          };
          return BookingModel.fromJson(bookingData);
        }).toList();
      } catch (fallbackError) {
        if (kDebugMode) {
          print('❌ Database function fallback also failed: $fallbackError');
        }
        return [];
      }
    }
  }

  /// Cancel a booking (for passengers)
  static Future<Map<String, dynamic>> cancelBooking({
    required String bookingId,
    String? cancellationReason,
  }) async {
    try {
      if (kDebugMode) {
        print('🚫 Cancelling booking: $bookingId');
      }

      // Get booking details first
      final booking = await getBookingById(bookingId);
      if (booking == null) {
        return {
          'success': false,
          'error': 'الحجز غير موجود',
        };
      }

      // Update booking status
      await _client.from('bookings').update({
        'status': 'cancelled',
        'cancelled_at': DateTime.now().toIso8601String(),
        'rejection_reason': cancellationReason,
      }).eq('id', bookingId);

      // If booking was accepted, restore the seats
      if (booking.status == 'accepted') {
        await _client.from('trips').update({
          'available_seats': booking.trip!.availableSeats + booking.seatsBooked
        }).eq('id', booking.tripId);
      }

      if (kDebugMode) {
        print('✅ Booking cancelled successfully');
      }

      return {
        'success': true,
        'message': 'تم إلغاء الحجز بنجاح',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error cancelling booking: $e');
      }
      return {
        'success': false,
        'error': 'حدث خطأ أثناء إلغاء الحجز: $e',
      };
    }
  }

  /// Mark booking as completed
  static Future<Map<String, dynamic>> completeBooking(String bookingId) async {
    try {
      if (kDebugMode) {
        print('✅ Completing booking: $bookingId');
      }

      await _client.from('bookings').update({
        'status': 'completed',
        'completed_at': DateTime.now().toIso8601String(),
      }).eq('id', bookingId);

      if (kDebugMode) {
        print('✅ Booking completed successfully');
      }

      return {
        'success': true,
        'message': 'تم إكمال الرحلة بنجاح',
      };
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error completing booking: $e');
      }
      return {
        'success': false,
        'error': 'حدث خطأ أثناء إكمال الحجز: $e',
      };
    }
  }
}
